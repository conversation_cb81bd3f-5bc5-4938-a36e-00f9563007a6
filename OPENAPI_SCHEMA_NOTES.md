# OpenAPI Schema Writing Guidelines - AS Project

## 📁 **<PERSON><PERSON><PERSON> trúc thư mục**

```
packages/as-schema/src/
├── admin/                          # Admin API specifications
│   ├── index.yml                   # Main specification file
│   ├── paths/                      # API endpoint definitions
│   │   └── [module]/[endpoint]/
│   │       ├── get.yml            # GET method
│   │       ├── post.yml           # POST method  
│   │       ├── put.yml            # PUT method
│   │       └── delete.yml         # DELETE method
│   └── components/                 # Reusable schema components
│       ├── models/                # Response/Data models
│       │   └── [module]/
│       │       └── model-name.yml
│       ├── requests/              # Request schemas
│       │   └── [module]/[endpoint]/
│       │       └── method.request.yml
│       └── responses/             # Response schemas (if needed)
└── user/                          # User API specifications
    └── [same structure as admin]
```

## 🔧 **Naming Conventions**

### **Files & Directories:**
- **Modules**: `kebab-case` (system, user-management)
- **Endpoints**: `kebab-case` (maintenance-status, user-profile)
- **Methods**: `lowercase` (get.yml, post.yml, put.yml, delete.yml)
- **Models**: `kebab-case` (maintenance-status.yml, user-profile.yml)
- **Requests**: `method.request.yml` (post.request.yml, put.request.yml)

### **Schema Names (in index.yml):**
- **Models**: `PascalCase` (MaintenanceStatus, UserProfile)
- **Requests**: `PascalCase + Request` (MaintenanceUpdateRequest, UserCreateRequest)
- **Responses**: `PascalCase + Response` (UserListResponse, ProductSearchResponse)

### **operationId:**
- **Format**: `kebab-case` (get-maintenance-status, update-user-profile)
- **Pattern**: `[method]-[resource]-[action]` nếu cần

## 📝 **Template cho Path Files**

### **GET Endpoint:**
```yaml
summary: Get [resource name]
description: Retrieve [detailed description]
operationId: get-[resource-name]
tags:
  - [module-name]
responses:
  "200":
    description: Successfully retrieved [resource]
    content:
      application/json:
        schema:
          $ref: "../../index.yml#/components/schemas/[ModelName]"
  "400":
    description: Bad Request
    content:
      application/json:
        schema:
          $ref: "../../index.yml#/components/schemas/ErrorResponse"
  "401":
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: "../../index.yml#/components/schemas/ErrorResponse"
  "500":
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: "../../index.yml#/components/schemas/ErrorResponse"
```

### **POST/PUT Endpoint:**
```yaml
summary: [Action] [resource name]
description: [Detailed description of action]
operationId: [action]-[resource-name]
tags:
  - [module-name]
requestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: "../../index.yml#/components/schemas/[RequestName]"
responses:
  "200":
    description: Successfully [action] [resource]
    content:
      application/json:
        schema:
          $ref: "../../index.yml#/components/schemas/[ModelName]"
  "400":
    description: Bad Request
    content:
      application/json:
        schema:
          $ref: "../../index.yml#/components/schemas/ErrorResponse"
  # ... other error responses
```

## 📋 **Template cho Component Files**

### **Model File (components/models/[module]/model-name.yml):**
```yaml
type: object
properties:
  id:
    type: string
    description: Unique identifier
    example: "123e4567-e89b-12d3-a456-426614174000"
  name:
    type: string
    description: Name of the resource
    example: "Example Name"
  isActive:
    type: boolean
    description: Whether the resource is active
    example: true
  createdAt:
    type: string
    format: date-time
    description: When the resource was created
    example: "2024-01-01T10:00:00Z"
  optionalField:
    type: string
    description: Optional field description
    example: "Optional value"
    nullable: true
required:
  - id
  - name
  - isActive
```

### **Request File (components/requests/[module]/[endpoint]/method.request.yml):**
```yaml
type: object
properties:
  name:
    type: string
    description: Name to set
    example: "New Name"
  isActive:
    type: boolean
    description: Set active status
    example: true
required:
  - name
```

## 🔗 **Reference Patterns**

### **Trong Path Files:**
```yaml
# Reference đến schema trong index.yml
$ref: "../../index.yml#/components/schemas/ModelName"
$ref: "../../../index.yml#/components/schemas/ModelName"  # Tùy độ sâu thư mục
```

### **Trong index.yml:**
```yaml
components:
  schemas:
    ModelName:
      $ref: ./components/models/module/model-name.yml
    RequestName:
      $ref: ./components/requests/module/endpoint/method.request.yml
```

## 🏷️ **Tags & Organization**

### **Common Tags:**
- `auth` - Authentication endpoints
- `system` - System management
- `user` - User management  
- `product` - Product operations
- `order` - Order management
- `admin` - Admin-specific operations

### **Trong index.yml:**
```yaml
tags:
  - name: auth
  - name: system
  - name: user
  # ... other tags
```

## ⚠️ **Important Rules**

### **DO:**
- ✅ Sử dụng `kebab-case` cho files, directories, operationId
- ✅ Sử dụng `PascalCase` cho schema names trong index.yml
- ✅ Reference từ paths về `index.yml#/components/schemas/`
- ✅ Tạo file components riêng cho models và requests
- ✅ Luôn có description và example cho properties
- ✅ Specify required fields
- ✅ Sử dụng nullable: true cho optional fields có thể null

### **DON'T:**
- ❌ Định nghĩa schema trực tiếp trong index.yml
- ❌ Reference trực tiếp đến component files từ paths
- ❌ Sử dụng camelCase cho file names
- ❌ Bỏ qua error response schemas
- ❌ Thiếu description cho endpoints và properties

## 🔄 **Development Workflow**

### **1. Tạo mới API:**
```bash
# 1. Tạo path files
mkdir -p src/admin/paths/[module]/[endpoint]
touch src/admin/paths/[module]/[endpoint]/{get,post,put,delete}.yml

# 2. Tạo component files  
mkdir -p src/admin/components/models/[module]
mkdir -p src/admin/components/requests/[module]/[endpoint]
touch src/admin/components/models/[module]/model-name.yml
touch src/admin/components/requests/[module]/[endpoint]/method.request.yml

# 3. Update index.yml
# - Add paths
# - Add schemas to components
# - Add tags if needed
```

### **2. Build & Generate:**
```bash
# Build schema
docker exec -it api.as.dev make build-schema

# Generate API code
docker exec -it api.as.dev make api-doc ROLE=admin
docker exec -it api.as.dev make dtogen ROLE=admin  
docker exec -it api.as.dev make apigen ROLE=admin tag=module-name query=1
```

## 📋 **Checklist**

Trước khi commit schema:

- [ ] operationId sử dụng kebab-case
- [ ] Schema names sử dụng PascalCase
- [ ] File names sử dụng kebab-case
- [ ] Paths reference đến index.yml
- [ ] Index.yml reference đến component files
- [ ] Tất cả properties có description và example
- [ ] Required fields được specify
- [ ] Error responses được define
- [ ] Tags được thêm vào index.yml
- [ ] Schema build thành công

---

**Ghi chú**: Luôn follow pattern hiện có trong codebase. Khi có thắc mắc, tham khảo existing endpoints như `/auth` làm mẫu.

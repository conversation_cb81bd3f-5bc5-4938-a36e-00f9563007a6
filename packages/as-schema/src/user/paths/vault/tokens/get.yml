operationId: get-vault-tokens
summary: Get Vault Tokens
description: Retrieve vault token data for the authenticated user
tags:
  - payment
security:
  - bearerAuth: []
responses:
  '200':
    description: Successfully retrieved vault tokens
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/GetVaultTokensResponse'
  '400':
    description: Bad Request (Validation Error)
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '401':
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '403':
    description: Forbidden
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '404':
    description: No vault tokens found
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '500':
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'
  '503':
    description: Service Unavailable
    content:
      application/json:
        schema:
          $ref: '../../../index.yml#/components/schemas/ErrorResponse'

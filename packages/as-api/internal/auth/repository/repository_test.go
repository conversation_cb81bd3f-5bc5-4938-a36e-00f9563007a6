package repository

import (
	"context"
	"testing"
	"time"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestFindOneByUsername(t *testing.T) {
	t.<PERSON>llel()

	m, err := factory.SetUpMock(nil)
	require.NoError(t, err)

	r := New(m.DO)
	ctx := context.TODO()

	cols := []string{"id", "username", "password", "user_id", "admin_id", "created_at", "updated_at", "deleted_at"}

	tests := []struct {
		name    string
		mock    func()
		input   string
		want    *entities.Auth
		wantErr error
	}{
		{
			name:  "SUCCESS - Find auth by username",
			input: "testuser",
			mock: func() {
				now := time.Now()
				rows := sqlmock.NewRows(cols).
					AddRow("auth123", "testuser", "hashedpass", "user123", nil, now, now, nil)
				m.Mock.ExpectQuery(`SELECT \* FROM "auth" WHERE "auth"."username" = \$1 AND "auth"."deleted_at" IS NULL ORDER BY "auth"."id" LIMIT \$2`).
					WithArgs("testuser", 1).
					WillReturnRows(rows)
			},
			want: &entities.Auth{
				ID:       pointer.Ptr("auth123"),
				Username: "testuser",
				Password: "hashedpass",
				UserID:   pointer.Ptr("user123"),
				AdminID:  nil,
			},
			wantErr: nil,
		},
		{
			name:  "FAILED - NOT FOUND auth",
			input: "nonexistent",
			mock: func() {
				m.Mock.ExpectQuery(`SELECT \* FROM "auth" WHERE "auth"."username" = \$1 AND "auth"."deleted_at" IS NULL ORDER BY "auth"."id" LIMIT \$2`).
					WithArgs("nonexistent", 1).
					WillReturnRows(sqlmock.NewRows(cols))
			},
			want:    nil,
			wantErr: apiutil.ErrResourceNotFound,
		},
		{
			name:  "FAILED - Database error",
			input: "testuser",
			mock: func() {
				m.Mock.ExpectQuery(`SELECT \* FROM "auth" WHERE "auth"."username" = \$1 AND "auth"."deleted_at" IS NULL ORDER BY "auth"."id" LIMIT \$2`).
					WithArgs("testuser", 1).
					WillReturnError(assert.AnError)
			},
			want:    nil,
			wantErr: assert.AnError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			got, err := r.FindOneByUsername(ctx, tt.input)

			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.Equal(t, tt.want.Username, got.Username)
				assert.Equal(t, tt.want.Password, got.Password)
				assert.Equal(t, tt.want.UserID, got.UserID)
				assert.Equal(t, tt.want.AdminID, got.AdminID)
			}
		})
	}

	assert.NoError(t, m.Mock.ExpectationsWereMet())
}

func TestFindOneByToken(t *testing.T) {
	t.Parallel()

	m, err := factory.SetUpMock(nil)
	require.NoError(t, err)

	r := New(m.DO)
	ctx := context.TODO()

	cols := []string{"id", "username", "password", "user_id", "admin_id", "created_at", "updated_at", "deleted_at"}

	tests := []struct {
		name    string
		mock    func()
		input   string
		want    *entities.Auth
		wantErr error
	}{
		{
			name:  "SUCCESS - Find auth by token",
			input: "valid-token",
			mock: func() {
				now := time.Now()
				rows := sqlmock.NewRows(cols).
					AddRow("auth123", "testuser", "hashedpass", "user123", nil, now, now, nil)
				m.Mock.ExpectQuery(`SELECT "auth"\."id","auth"\."created_at","auth"\."updated_at","auth"\."deleted_at","auth"\."user_id","auth"\."admin_id","auth"\."username","auth"\."password","auth"\."account_id" FROM "auth" INNER JOIN "refresh_tokens" ON "refresh_tokens"\."auth_id" = "auth"\."id" WHERE "refresh_tokens"\."token" = \$1 AND "auth"\."deleted_at" IS NULL ORDER BY "auth"\."id" LIMIT \$2`).
					WithArgs("valid-token", 1).
					WillReturnRows(rows)
			},
			want: &entities.Auth{
				ID:       pointer.Ptr("auth123"),
				Username: "testuser",
				Password: "hashedpass",
				UserID:   pointer.Ptr("user123"),
				AdminID:  nil,
			},
			wantErr: nil,
		},
		{
			name:  "FAILED - NOT FOUND token",
			input: "invalid-token",
			mock: func() {
				m.Mock.ExpectQuery(`SELECT "auth"\."id","auth"\."created_at","auth"\."updated_at","auth"\."deleted_at","auth"\."user_id","auth"\."admin_id","auth"\."username","auth"\."password","auth"\."account_id" FROM "auth" INNER JOIN "refresh_tokens" ON "refresh_tokens"\."auth_id" = "auth"\."id" WHERE "refresh_tokens"\."token" = \$1 AND "auth"\."deleted_at" IS NULL ORDER BY "auth"\."id" LIMIT \$2`).
					WithArgs("invalid-token", 1).
					WillReturnRows(sqlmock.NewRows(cols))
			},
			want:    nil,
			wantErr: apiutil.ErrResourceNotFound,
		},
		{
			name:  "FAILED - Database error",
			input: "valid-token",
			mock: func() {
				m.Mock.ExpectQuery(`SELECT "auth"\."id","auth"\."created_at","auth"\."updated_at","auth"\."deleted_at","auth"\."user_id","auth"\."admin_id","auth"\."username","auth"\."password","auth"\."account_id" FROM "auth" INNER JOIN "refresh_tokens" ON "refresh_tokens"\."auth_id" = "auth"\."id" WHERE "refresh_tokens"\."token" = \$1 AND "auth"\."deleted_at" IS NULL ORDER BY "auth"\."id" LIMIT \$2`).
					WithArgs("valid-token", 1).
					WillReturnError(assert.AnError)
			},
			want:    nil,
			wantErr: assert.AnError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			got, err := r.FindOneByToken(ctx, tt.input)

			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.Equal(t, tt.want.Username, got.Username)
				assert.Equal(t, tt.want.Password, got.Password)
				assert.Equal(t, tt.want.UserID, got.UserID)
				assert.Equal(t, tt.want.AdminID, got.AdminID)
			}
		})
	}

	assert.NoError(t, m.Mock.ExpectationsWereMet())
}

func TestSaveRefreshToken(t *testing.T) {
	t.Parallel()

	m, err := factory.SetUpMock(nil)
	require.NoError(t, err)

	r := New(m.DO)
	ctx := context.TODO()

	tests := []struct {
		name    string
		mock    func()
		input   *entities.RefreshToken
		wantErr error
	}{
		{
			name: "SUCCESS - Save refresh token",
			input: &entities.RefreshToken{
				AuthID: pointer.Ptr("auth123"),
				Token:  "new-token",
			},
			mock: func() {
				m.Mock.ExpectQuery(`INSERT INTO "refresh_tokens" \("deleted_at","auth_id","token"\) VALUES \(\$1,\$2,\$3\) RETURNING "id","created_at","updated_at"`).
					WithArgs(nil, "auth123", "new-token").
					WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "updated_at"}).
						AddRow(1, time.Now(), time.Now()))
			},
			wantErr: nil,
		},
		{
			name: "FAILED - Database error",
			input: &entities.RefreshToken{
				AuthID: pointer.Ptr("auth123"),
				Token:  "new-token",
			},
			mock: func() {
				m.Mock.ExpectQuery(`INSERT INTO "refresh_tokens" \("deleted_at","auth_id","token"\) VALUES \(\$1,\$2,\$3\) RETURNING "id","created_at","updated_at"`).
					WithArgs(nil, "auth123", "new-token").
					WillReturnError(assert.AnError)
			},
			wantErr: assert.AnError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			err := r.SaveRefreshToken(ctx, tt.input)

			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}

	assert.NoError(t, m.Mock.ExpectationsWereMet())
}

func TestSavePassword(t *testing.T) {
	t.Parallel()

	m, err := factory.SetUpMock(nil)
	require.NoError(t, err)

	r := New(m.DO)
	ctx := context.TODO()

	tests := []struct {
		name     string
		authID   string
		password string
		mock     func()
		wantErr  error
	}{
		{
			name:     "SUCCESS - Save password",
			authID:   "auth123",
			password: "hashedpassword",
			mock: func() {
				m.Mock.ExpectExec(`UPDATE "auth" SET "updated_at"=\$1,"password"=\$2 WHERE "auth"."id" = \$3 AND "auth"."deleted_at" IS NULL`).
					WithArgs(sqlmock.AnyArg(), "hashedpassword", "auth123").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: nil,
		},
		{
			name:     "FAILED - Database error",
			authID:   "auth123",
			password: "hashedpassword",
			mock: func() {
				m.Mock.ExpectExec(`UPDATE "auth" SET "updated_at"=\$1,"password"=\$2 WHERE "auth"."id" = \$3 AND "auth"."deleted_at" IS NULL`).
					WithArgs(sqlmock.AnyArg(), "hashedpassword", "auth123").
					WillReturnError(assert.AnError)
			},
			wantErr: assert.AnError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			err := r.SavePassword(ctx, tt.authID, tt.password)

			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}

	assert.NoError(t, m.Mock.ExpectationsWereMet())
}

func TestFindOneByUserID(t *testing.T) {
	t.Parallel()

	m, err := factory.SetUpMock(nil)
	require.NoError(t, err)

	r := New(m.DO)
	ctx := context.TODO()

	cols := []string{"id", "username", "password", "user_id", "admin_id", "created_at", "updated_at", "deleted_at"}

	tests := []struct {
		name    string
		mock    func()
		input   string
		want    *entities.Auth
		wantErr error
	}{
		{
			name:  "SUCCESS - Find auth by user ID",
			input: "user123",
			mock: func() {
				now := time.Now()
				rows := sqlmock.NewRows(cols).
					AddRow("auth123", "testuser", "hashedpass", "user123", nil, now, now, nil)
				m.Mock.ExpectQuery(`SELECT \* FROM "auth" WHERE "auth"."user_id" = \$1 AND "auth"."deleted_at" IS NULL ORDER BY "auth"."id" LIMIT \$2`).
					WithArgs("user123", 1).
					WillReturnRows(rows)
			},
			want: &entities.Auth{
				ID:       pointer.Ptr("auth123"),
				Username: "testuser",
				Password: "hashedpass",
				UserID:   pointer.Ptr("user123"),
				AdminID:  nil,
			},
			wantErr: nil,
		},
		{
			name:  "FAILED - NOT FOUND user ID",
			input: "nonexistent",
			mock: func() {
				m.Mock.ExpectQuery(`SELECT \* FROM "auth" WHERE "auth"."user_id" = \$1 AND "auth"."deleted_at" IS NULL ORDER BY "auth"."id" LIMIT \$2`).
					WithArgs("nonexistent", 1).
					WillReturnRows(sqlmock.NewRows(cols))
			},
			want:    nil,
			wantErr: apiutil.ErrResourceNotFound,
		},
		{
			name:  "FAILED - Database error",
			input: "user123",
			mock: func() {
				m.Mock.ExpectQuery(`SELECT \* FROM "auth" WHERE "auth"."user_id" = \$1 AND "auth"."deleted_at" IS NULL ORDER BY "auth"."id" LIMIT \$2`).
					WithArgs("user123", 1).
					WillReturnError(assert.AnError)
			},
			want:    nil,
			wantErr: assert.AnError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			got, err := r.FindOneByUserID(ctx, tt.input)

			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.Equal(t, tt.want.Username, got.Username)
				assert.Equal(t, tt.want.Password, got.Password)
				assert.Equal(t, tt.want.UserID, got.UserID)
				assert.Equal(t, tt.want.AdminID, got.AdminID)
			}
		})
	}

	assert.NoError(t, m.Mock.ExpectationsWereMet())
}

func TestDeleteRefreshToken(t *testing.T) {
	t.Parallel()

	m, err := factory.SetUpMock(nil)
	require.NoError(t, err)

	r := New(m.DO)
	ctx := context.TODO()

	tests := []struct {
		name    string
		mock    func()
		input   string
		wantErr error
	}{
		{
			name:  "SUCCESS - Delete refresh token",
			input: "valid-token",
			mock: func() {
				m.Mock.ExpectExec(`UPDATE "refresh_tokens" SET "deleted_at"=\$1 WHERE "refresh_tokens"."token" = \$2 AND "refresh_tokens"."deleted_at" IS NULL`).
					WithArgs(sqlmock.AnyArg(), "valid-token").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: nil,
		},
		{
			name:  "FAILED - Database error",
			input: "valid-token",
			mock: func() {
				m.Mock.ExpectExec(`UPDATE "refresh_tokens" SET "deleted_at"=\$1 WHERE "refresh_tokens"."token" = \$2 AND "refresh_tokens"."deleted_at" IS NULL`).
					WithArgs(sqlmock.AnyArg(), "valid-token").
					WillReturnError(assert.AnError)
			},
			wantErr: assert.AnError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			err := r.DeleteRefreshToken(ctx, tt.input)

			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}

	assert.NoError(t, m.Mock.ExpectationsWereMet())
}

func TestDeleteAuthByUserID(t *testing.T) {
	t.Parallel()

	m, err := factory.SetUpMock(nil)
	require.NoError(t, err)

	r := New(m.DO)
	ctx := context.TODO()

	tests := []struct {
		name    string
		mock    func()
		input   string
		wantErr error
	}{
		{
			name:  "SUCCESS - Delete auth by user ID",
			input: "user123",
			mock: func() {
				m.Mock.ExpectExec(`UPDATE "auth" SET "deleted_at"=\$1 WHERE "auth"."user_id" = \$2 AND "auth"."deleted_at" IS NULL`).
					WithArgs(sqlmock.AnyArg(), "user123").
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: nil,
		},
		{
			name:  "SUCCESS - Delete auth by user ID with no matching records",
			input: "nonexistent-user",
			mock: func() {
				m.Mock.ExpectExec(`UPDATE "auth" SET "deleted_at"=\$1 WHERE "auth"."user_id" = \$2 AND "auth"."deleted_at" IS NULL`).
					WithArgs(sqlmock.AnyArg(), "nonexistent-user").
					WillReturnResult(sqlmock.NewResult(0, 0))
			},
			wantErr: nil,
		},
		{
			name:  "FAILED - Database error",
			input: "user123",
			mock: func() {
				m.Mock.ExpectExec(`UPDATE "auth" SET "deleted_at"=\$1 WHERE "auth"."user_id" = \$2 AND "auth"."deleted_at" IS NULL`).
					WithArgs(sqlmock.AnyArg(), "user123").
					WillReturnError(assert.AnError)
			},
			wantErr: assert.AnError,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			err := r.DeleteAuthByUserID(ctx, tt.input)

			if tt.wantErr != nil {
				assert.ErrorIs(t, err, tt.wantErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}

	assert.NoError(t, m.Mock.ExpectationsWereMet())
}

package paypal

import (
	"context"
	"slices"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/logger"
	"as-api/as/foundations/paypalapi"
	"as-api/as/internal/paypal/repository"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

type paypalDomain struct {
	repo         repository.Repository
	paypalClient paypalapi.PayPalClient
	log          logger.Logger
}

func NewPayPalDomain(repo repository.Repository, paypalClient paypalapi.PayPalClient, log logger.Logger) PayPalDomain {
	return &paypalDomain{
		repo:         repo,
		paypalClient: paypalClient,
		log:          log,
	}
}

// PayPal API operations
func (d *paypalDomain) CreateVaultSetupToken(ctx context.Context, req *CreateVaultSetupTokenRequest) (*CreateVaultSetupTokenResponse, error) {
	// Convert request to generated types
	setupTokenReq := convertToSetupTokenRequest(req)

	// Call the generated client with authentication
	resp, err := d.paypalClient.SetupTokensCreate(ctx, setupTokenReq)
	if err != nil {
		return nil, errors.Wrap(err, "create setup token request")
	}

	return convertFromSetupTokenResponse(resp), nil
}

func (d *paypalDomain) CreateVaultPaymentToken(ctx context.Context, req *CreateVaultPaymentTokenRequest) (*CreateVaultPaymentTokenResponse, error) {
	// Convert request to generated types
	paymentTokenReq := convertToPaymentTokenRequest(req)

	// Call the generated client with authentication
	resp, err := d.paypalClient.CreateVaultPaymentToken(ctx, paymentTokenReq)
	if err != nil {
		return nil, errors.Wrap(err, "create payment token request")
	}

	return convertFromPaymentTokenResponse(resp), nil
}

func (d *paypalDomain) StoreVaultToken(ctx context.Context, customerID string, paypalResp *CreateVaultPaymentTokenResponse) (*VaultTokenData, error) {
	if customerID == "" {
		return nil, errors.New("customer ID is empty")
	}

	if paypalResp == nil {
		return nil, errors.New("paypal response is nil")
	}

	if paypalResp.ID == "" {
		return nil, errors.New("vault card ID is empty")
	}

	// Extract card information from PayPal response
	var cardInfo *entities.JSON
	if paypalResp.PaymentSource.Card != nil {
		cardInfoData := &CardInfo{
			Brand:      pointer.PtrString[string](paypalResp.PaymentSource.Card.Brand),
			LastDigits: paypalResp.PaymentSource.Card.LastDigits,
			Name:       paypalResp.PaymentSource.Card.Name,
		}

		// Convert expiry to string if available
		if paypalResp.PaymentSource.Card.Expiry != nil {
			cardInfoData.Expiry = pointer.Ptr(string(*paypalResp.PaymentSource.Card.Expiry))
		}

		// Convert to JSON
		cardInfoJSON, err := entities.ConvertDataToJSON(cardInfoData)
		if err != nil {
			return nil, errors.Wrap(err, "failed to marshal card info")
		}

		cardInfo = cardInfoJSON
	}

	// Create PaypalUser entity
	paypalUser := &entities.PaypalUser{
		CustomerID:  customerID,
		VaultCardID: paypalResp.ID,
		CardInfo:    cardInfo,
	}

	// Store in database
	result, err := d.repo.CreateVaultToken(ctx, paypalUser)
	if err != nil {
		d.log.Error("failed to store vault token", logger.FieldMap{
			"customer_id":   customerID,
			"vault_card_id": paypalResp.ID,
			"error":         err,
		})
		return nil, errors.Wrap(err, "failed to store vault token")
	}

	// Clean up old vault tokens
	go d.CleanUpVaultToken(ctx, customerID, paypalResp.ID)

	return convertFromVaultToken(result)
}

func (d *paypalDomain) GetVaultTokenByCustomerID(ctx context.Context, customerID string) (*VaultTokenData, error) {
	if customerID == "" {
		return nil, errors.New("customer ID is empty")
	}

	result, err := d.repo.FindByCustomerID(ctx, customerID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get vault token by customer ID")
	}

	if len(result) == 0 {
		return nil, nil
	}

	return convertFromVaultToken(result[0])
}

func (d *paypalDomain) CleanUpVaultToken(ctx context.Context, customerID string, exceptVaultCardID ...string) {
	existings, err := d.repo.FindByCustomerID(ctx, customerID)
	if err != nil {
		d.log.Error("failed to get vault token by customer ID", logger.FieldMap{
			"customer_id": customerID,
			"error":       err,
		})
		return
	}

	for _, existing := range existings {
		if slices.Contains(exceptVaultCardID, existing.VaultCardID) {
			continue
		}

		if err := d.repo.DeleteVaultToken(ctx, existing.VaultCardID); err != nil {
			d.log.Error("failed to delete vault token from database", logger.FieldMap{
				"customer_id":   customerID,
				"vault_card_id": existing.VaultCardID,
				"error":         err,
			})
		}

		if err := d.paypalClient.DeleteVaultPaymentToken(ctx, existing.VaultCardID); err != nil {
			d.log.Error("failed to delete vault token from paypal", logger.FieldMap{
				"customer_id":   customerID,
				"vault_card_id": existing.VaultCardID,
				"error":         err,
			})
		}
	}
}

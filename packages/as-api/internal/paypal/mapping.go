package paypal

import (
	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/paypalapi"
	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

// convertFromSetupTokenResponse converts PayPal generated response to domain response
func convertFromSetupTokenResponse(resp *paypalapi.SetupTokenResponse) *CreateVaultSetupTokenResponse {
	result := &CreateVaultSetupTokenResponse{
		ID:     *resp.Id,
		Status: string(*resp.Status),
		Links:  []Link{},
	}

	// Convert payment source
	if resp.PaymentSource != nil {
		result.PaymentSource.Card = resp.PaymentSource.Card
	}

	// Convert links
	for _, link := range pointer.Safe(resp.Links) {
		result.Links = append(result.Links, Link{
			Href:   link.Href,
			Rel:    link.Rel,
			Method: pointer.SafeString[string](link.Method),
		})
	}

	return result
}

// convertToSetupTokenRequest converts domain request to PayPal generated request
func convertToSetupTokenRequest(req *CreateVaultSetupTokenRequest) paypalapi.SetupTokenRequest {
	var setupReq paypalapi.SetupTokenRequest

	cardReq := req.PaymentSource.Card
	if cardReq == nil {
		cardReq = &paymenttokens.CardRequest{}
	}

	// Enable SCA for the card
	cardReq.VerificationMethod = pointer.Ptr("SCA_ALWAYS")

	if req.ExperienceContext != nil {
		cardReq.ExperienceContext = &paymenttokens.ExperienceContext{
			ReturnUrl: req.ExperienceContext.ReturnUrl,
			CancelUrl: req.ExperienceContext.CancelUrl,
		}
	}

	// Set the card request
	setupReq.PaymentSource.Card = cardReq

	// Set customer
	if req.Customer != nil {
		setupReq.Customer = &paymenttokens.Customer{
			Id: pointer.Ptr(req.Customer.ID),
		}
	}

	return setupReq
}

// convertFromPaymentTokenResponse converts PayPal generated response to domain response
func convertFromPaymentTokenResponse(resp *paypalapi.PaymentTokenResponse) *CreateVaultPaymentTokenResponse {
	result := &CreateVaultPaymentTokenResponse{
		ID:    *resp.Id,
		Links: []Link{},
	}

	// Convert customer
	if resp.Customer != nil && resp.Customer.Id != nil {
		result.Customer = Customer{
			ID: *resp.Customer.Id,
		}
	}

	// Convert payment source
	if resp.PaymentSource != nil {
		result.PaymentSource = PaymentTokenResponseSource{
			Card: resp.PaymentSource.Card,
		}
	}

	// Convert links
	for _, link := range *resp.Links {
		result.Links = append(result.Links, Link{
			Href:   link.Href,
			Rel:    link.Rel,
			Method: pointer.SafeString[string](link.Method),
		})
	}

	return result
}

// convertToPaymentTokenRequest converts domain request to PayPal generated request
func convertToPaymentTokenRequest(req *CreateVaultPaymentTokenRequest) paypalapi.PaymentTokenRequest {
	var tokenReq paypalapi.PaymentTokenRequest

	tokenReq.PaymentSource.Token = &paymenttokens.TokenIdRequest{
		Id:   req.PaymentSource.Token.ID,
		Type: paymenttokens.TokenIdRequestType(req.PaymentSource.Token.Type),
	}

	return tokenReq
}

func convertFromVaultToken(paypalUser *entities.PaypalUser) (*VaultTokenData, error) {
	cardInfo, err := entities.ConvertJSONToData[CardInfo](paypalUser.CardInfo)
	if err != nil {
		return nil, errors.Wrap(err, "failed to convert card info to JSON")
	}

	return &VaultTokenData{
		CustomerID:  paypalUser.CustomerID,
		VaultCardID: paypalUser.VaultCardID,
		CardInfo:    cardInfo,
	}, nil
}

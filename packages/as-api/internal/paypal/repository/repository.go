package repository

import (
	"context"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"

	"github.com/pkg/errors"
)

type Repository interface {
	CreateVaultToken(ctx context.Context, paypalUser *entities.PaypalUser) (*entities.PaypalUser, error)
	FindByCustomerID(ctx context.Context, customerID string) ([]*entities.PaypalUser, error)
	DeleteVaultToken(ctx context.Context, id string) error
}

type repository struct {
	db *factory.DO
}

func NewRepository(db *factory.DO) Repository {
	return &repository{
		db: db,
	}
}

func (r *repository) CreateVaultToken(ctx context.Context, paypalUser *entities.PaypalUser) (*entities.PaypalUser, error) {
	db := r.db.Model(nil)

	if err := db.Query.PaypalUser.WithContext(ctx).Create(paypalUser); err != nil {
		return nil, errors.Wrap(err, "create paypal user vault token")
	}

	return paypalUser, nil
}

func (r *repository) FindByCustomerID(ctx context.Context, customerID string) ([]*entities.PaypalUser, error) {
	db := r.db.Model(nil)

	p := db.Query.PaypalUser

	q := p.WithContext(ctx).Where(p.CustomerID.Eq(customerID))

	paypalUsers, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "find paypal user by customer id")
	}

	return paypalUsers, nil
}

func (r *repository) DeleteVaultToken(ctx context.Context, id string) error {
	db := r.db.Model(nil)

	p := db.Query.PaypalUser

	_, err := p.WithContext(ctx).Where(p.ID.Eq(id)).Delete()
	if err != nil {
		return errors.Wrap(err, "delete paypal user vault token")
	}

	return nil
}

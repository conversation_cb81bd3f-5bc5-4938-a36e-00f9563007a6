package paypal

import (
	"context"

	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"
)

// Constants for PayPal operations
const (
	TokenTypeSetupToken = "SETUP_TOKEN"
)

// PayPal request/response types for domain layer
type CardRequest = paymenttokens.CardRequest
type CardResponse = paymenttokens.CardResponse
type AddressPortable = paymenttokens.AddressPortable
type CountryCode = paymenttokens.CountryCode

type PaymentSourceRequest struct {
	Card *CardRequest `json:"card"`
}

type PaymentSourceResponse struct {
	Card *CardResponse `json:"card"`
}

type PaymentTokenSource struct {
	Token Token `json:"token"`
}

type Token struct {
	ID   string `json:"id"`
	Type string `json:"type"`
}

type Customer struct {
	ID string `json:"id"`
}

type PaymentTokenResponseSource struct {
	Card *CardResponse `json:"card"`
}

type Link struct {
	Href   string `json:"href"`
	Rel    string `json:"rel"`
	Method string `json:"method"`
}

// ExperienceContext Customizes the Vault creation flow experience for your customers.
type ExperienceContext struct {
	// BrandName The label that overrides the business name in the PayPal account on the PayPal site. The pattern is defined by an external party and supports Unicode.
	BrandName *string `json:"brand_name,omitempty"`

	// CancelUrl The URL where the customer is redirected after customer cancels or leaves the flow. It is a required field for contingency flows like PayPal wallet, 3DS.
	CancelUrl *string `json:"cancel_url,omitempty"`

	// Locale The [language tag](https://tools.ietf.org/html/bcp47#section-2) for the language in which to localize the error-related strings, such as messages, issues, and suggested actions. The tag is made up of the [ISO 639-2 language code](https://www.loc.gov/standards/iso639-2/php/code_list.php), the optional [ISO-15924 script tag](https://www.unicode.org/iso15924/codelists.html), and the [ISO-3166 alpha-2 country code](/api/rest/reference/country-codes/) or [M49 region code](https://unstats.un.org/unsd/methodology/m49/).
	Locale *string `json:"locale,omitempty"`

	// ReturnUrl The URL where the customer is redirected after customer approves leaves the flow. It is a required field for contingency flows like PayPal wallet, 3DS.
	ReturnUrl *string `json:"return_url,omitempty"`

	// ShippingPreference The shipping preference. This only applies to PayPal payment source.
	ShippingPreference *string `json:"shipping_preference,omitempty"`

	// VaultInstruction Vault Instruction on action to be performed after a successful payer approval.
	VaultInstruction *string `json:"vault_instruction,omitempty"`
}

type CreateVaultSetupTokenRequest struct {
	Customer          *Customer            `json:"customer"`
	PaymentSource     PaymentSourceRequest `json:"payment_source"`
	ExperienceContext *ExperienceContext   `json:"experience_context"`
}

type CreateVaultSetupTokenResponse struct {
	ID            string                `json:"id"`
	Status        string                `json:"status"`
	PaymentSource PaymentSourceResponse `json:"payment_source"`
	Links         []Link                `json:"links"`
}

type CreateVaultPaymentTokenRequest struct {
	PaymentSource PaymentTokenSource `json:"payment_source"`
}

type CreateVaultPaymentTokenResponse struct {
	ID            string                     `json:"id"`
	Status        string                     `json:"status"`
	Customer      Customer                   `json:"customer"`
	PaymentSource PaymentTokenResponseSource `json:"payment_source"`
	Links         []Link                     `json:"links"`
}

// VaultTokenData represents the data to be stored for a vault payment token
type VaultTokenData struct {
	CustomerID  string
	VaultCardID string
	CardInfo    *CardInfo
}

// CardInfo represents the card information from PayPal response
type CardInfo struct {
	Brand      *string `json:"brand,omitempty"`
	LastDigits *string `json:"last_digits,omitempty"`
	Name       *string `json:"name,omitempty"`
	Expiry     *string `json:"expiry,omitempty"`
}

type PayPalDomain interface {
	// PayPal API operations
	CreateVaultSetupToken(ctx context.Context, req *CreateVaultSetupTokenRequest) (*CreateVaultSetupTokenResponse, error)
	CreateVaultPaymentToken(ctx context.Context, req *CreateVaultPaymentTokenRequest) (*CreateVaultPaymentTokenResponse, error)

	// Repository operations
	StoreVaultToken(ctx context.Context, customerID string, paypalResp *CreateVaultPaymentTokenResponse) (*VaultTokenData, error)
	GetVaultTokenByCustomerID(ctx context.Context, customerID string) (*VaultTokenData, error)
}

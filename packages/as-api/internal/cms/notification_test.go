package cms

import (
	"context"
	"errors"
	"testing"
	"time"

	"as-api/as/foundations/db/entities"
	"as-api/as/mocks/domains/cms/repository"
	"as-api/as/pkg/helpers/pointer"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestDomain_GetNotifications(t *testing.T) {
	t.Parallel()

	mockRepo := repository.NewCmsRepository(t)
	domain := NewCmsDomain(mockRepo)
	ctx := context.Background()
	now := time.Now()

	tests := []struct {
		name     string
		params   GetNotificationsParams
		mockFunc func()
		want     struct {
			response *GetNotificationsResponse
			err      bool
		}
	}{
		{
			name: "SUCCESS - Get notifications",
			params: GetNotificationsParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			mockFunc: func() {
				mockRepo.On("GetNotifications", ctx, mock.Anything, 1, 10).Return([]*entities.Notification{
					{
						ID:        pointer.Ptr("1"),
						CreatedAt: pointer.Ptr(now),
						UpdatedAt: pointer.Ptr(now),
						Type:      "IMPORTANT_SYSTEM",
						Title:     entities.NewJSON(map[string]string{"en": "Test Notification"}),
						Message:   entities.NewJSON(map[string]string{"en": "Test Message"}),
						Data:      pointer.Ptr(entities.NewJSON(map[string]interface{}{"key": "value"})),
						Status:    pointer.Ptr("published"),
					},
				}, pointer.Ptr(1), nil).Once()
			},
			want: struct {
				response *GetNotificationsResponse
				err      bool
			}{
				response: &GetNotificationsResponse{
					Data: &[]*Notification{
						{
							ID:        "1",
							CreatedAt: now,
							UpdatedAt: now,
							Type:      "IMPORTANT_SYSTEM",
							Title:     map[string]string{"en": "Test Notification"},
							Message:   map[string]string{"en": "Test Message"},
							Data:      &map[string]any{"key": "value"},
							Status:    NotificationStatusPublished,
						},
					},
					Total: pointer.Ptr(1),
				},
				err: false,
			},
		},
		{
			name: "ERROR - Repository error",
			params: GetNotificationsParams{
				Page:  pointer.Ptr(1),
				Limit: pointer.Ptr(10),
			},
			mockFunc: func() {
				mockRepo.On("GetNotifications", ctx, mock.Anything, 1, 10).Return(nil, nil, errors.New("db error")).Once()
			},
			want: struct {
				response *GetNotificationsResponse
				err      bool
			}{
				response: nil,
				err:      true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			got, err := domain.GetNotifications(ctx, tt.params)

			if tt.want.err {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Len(t, *got.Data, len(*tt.want.response.Data))

				if len(*got.Data) > 0 && len(*tt.want.response.Data) > 0 {
					assert.Equal(t, (*tt.want.response.Data)[0].ID, (*got.Data)[0].ID)
					assert.Equal(t, (*tt.want.response.Data)[0].Type, (*got.Data)[0].Type)
					assert.Equal(t, (*tt.want.response.Data)[0].Title, (*got.Data)[0].Title)
					assert.Equal(t, (*tt.want.response.Data)[0].Status, (*got.Data)[0].Status)
				}
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestDomain_CreateNotification(t *testing.T) {
	t.Parallel()

	mockRepo := repository.NewCmsRepository(t)
	domain := NewCmsDomain(mockRepo)
	ctx := context.Background()
	now := time.Now()

	tests := []struct {
		name     string
		req      *Notification
		mockFunc func()
		want     struct {
			notification *Notification
			err          bool
		}
	}{
		{
			name: "SUCCESS - Create notification",
			req: &Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   map[string]string{"en": "Test Notification"},
				Message: map[string]string{"en": "Test Message"},
				Content: &map[string]NotificationArticleContent{
					"en": {
						Active:        true,
						Title:         "Test Title",
						Message:       "Test Message",
						KeyImage:      pointer.Ptr("image.jpg"),
						ArticleImages: []string{"image1.jpg", "image2.jpg"},
					},
				},
				Status: NotificationStatusDraft,
			},
			mockFunc: func() {
				mockRepo.On("CreateNotification", ctx, mock.AnythingOfType("*entities.Notification")).Return(&entities.Notification{
					ID:        pointer.Ptr("1"),
					CreatedAt: pointer.Ptr(now),
					UpdatedAt: pointer.Ptr(now),
					Type:      "IMPORTANT_SYSTEM",
					Title:     entities.NewJSON(map[string]string{"en": "Test Notification"}),
					Message:   entities.NewJSON(map[string]string{"en": "Test Message"}),
					Content: pointer.Ptr(entities.NewJSON(map[string]interface{}{
						"en": map[string]interface{}{
							"active":        true,
							"title":         "Test Title",
							"message":       "Test Message",
							"keyImage":      "image.jpg",
							"articleImages": []interface{}{"image1.jpg", "image2.jpg"},
						},
					})),
					Status: pointer.Ptr("draft"),
				}, nil).Once()
			},
			want: struct {
				notification *Notification
				err          bool
			}{
				notification: &Notification{
					ID:        "1",
					CreatedAt: now,
					UpdatedAt: now,
					Type:      "IMPORTANT_SYSTEM",
					Title:     map[string]string{"en": "Test Notification"},
					Message:   map[string]string{"en": "Test Message"},
					Content: &map[string]NotificationArticleContent{
						"en": {
							Active:        true,
							Title:         "Test Title",
							Message:       "Test Message",
							KeyImage:      pointer.Ptr("image.jpg"),
							ArticleImages: []string{"image1.jpg", "image2.jpg"},
						},
					},
					Status: NotificationStatusDraft,
				},
				err: false,
			},
		},
		{
			name: "ERROR - Repository error",
			req: &Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   map[string]string{"en": "Test Notification"},
				Message: map[string]string{"en": "Test Message"},
				Status:  NotificationStatusDraft,
			},
			mockFunc: func() {
				mockRepo.On("CreateNotification", ctx, mock.AnythingOfType("*entities.Notification")).Return(nil, errors.New("db error")).Once()
			},
			want: struct {
				notification *Notification
				err          bool
			}{
				notification: nil,
				err:          true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			got, err := domain.CreateNotification(ctx, tt.req)

			if tt.want.err {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.notification.ID, got.ID)
				assert.Equal(t, tt.want.notification.Type, got.Type)
				assert.Equal(t, tt.want.notification.Title, got.Title)
				assert.Equal(t, tt.want.notification.Status, got.Status)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestDomain_UpdateNotification(t *testing.T) {
	t.Parallel()

	mockRepo := repository.NewCmsRepository(t)
	domain := NewCmsDomain(mockRepo)
	ctx := context.Background()
	now := time.Now()
	id := "notification-123"

	tests := []struct {
		name     string
		id       string
		req      *Notification
		mockFunc func()
		want     struct {
			notification *Notification
			err          bool
		}
	}{
		{
			name: "SUCCESS - Update notification",
			id:   id,
			req: &Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   map[string]string{"en": "Updated Notification"},
				Message: map[string]string{"en": "Updated Message"},
				Status:  NotificationStatusPublished,
			},
			mockFunc: func() {
				mockRepo.On("UpdateNotification", ctx, id, mock.AnythingOfType("*entities.Notification")).Return(&entities.Notification{
					ID:        pointer.Ptr(id),
					CreatedAt: pointer.Ptr(now),
					UpdatedAt: pointer.Ptr(now),
					Type:      "IMPORTANT_SYSTEM",
					Title:     entities.NewJSON(map[string]string{"en": "Updated Notification"}),
					Message:   entities.NewJSON(map[string]string{"en": "Updated Message"}),
					Status:    pointer.Ptr("published"),
				}, nil).Once()
			},
			want: struct {
				notification *Notification
				err          bool
			}{
				notification: &Notification{
					ID:        id,
					CreatedAt: now,
					UpdatedAt: now,
					Type:      "IMPORTANT_SYSTEM",
					Title:     map[string]string{"en": "Updated Notification"},
					Message:   map[string]string{"en": "Updated Message"},
					Status:    NotificationStatusPublished,
				},
				err: false,
			},
		},
		{
			name: "ERROR - Repository error",
			id:   id,
			req: &Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   map[string]string{"en": "Updated Notification"},
				Message: map[string]string{"en": "Updated Message"},
				Status:  NotificationStatusPublished,
			},
			mockFunc: func() {
				mockRepo.On("UpdateNotification", ctx, id, mock.AnythingOfType("*entities.Notification")).Return(nil, errors.New("db error")).Once()
			},
			want: struct {
				notification *Notification
				err          bool
			}{
				notification: nil,
				err:          true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			got, err := domain.UpdateNotification(ctx, tt.id, tt.req)

			if tt.want.err {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.notification.ID, got.ID)
				assert.Equal(t, tt.want.notification.Type, got.Type)
				assert.Equal(t, tt.want.notification.Title, got.Title)
				assert.Equal(t, tt.want.notification.Status, got.Status)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestDomain_DeleteNotification(t *testing.T) {
	t.Parallel()

	mockRepo := repository.NewCmsRepository(t)
	domain := NewCmsDomain(mockRepo)
	ctx := context.Background()
	id := "notification-123"

	tests := []struct {
		name     string
		id       string
		mockFunc func()
		wantErr  bool
	}{
		{
			name: "SUCCESS - Delete notification",
			id:   id,
			mockFunc: func() {
				mockRepo.On("DeleteNotification", ctx, id).Return(nil).Once()
			},
			wantErr: false,
		},
		{
			name: "ERROR - Repository error",
			id:   id,
			mockFunc: func() {
				mockRepo.On("DeleteNotification", ctx, id).Return(errors.New("db error")).Once()
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			err := domain.DeleteNotification(ctx, tt.id)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

func TestDomain_GetNotificationById(t *testing.T) {
	t.Parallel()

	mockRepo := repository.NewCmsRepository(t)
	domain := NewCmsDomain(mockRepo)
	ctx := context.Background()

	now := time.Now()
	notificationID := "test-notification-id"

	tests := []struct {
		name    string
		id      string
		mock    func()
		want    *Notification
		wantErr error
	}{
		{
			name: "SUCCESS - Get notification by id",
			id:   notificationID,
			mock: func() {
				titleData := map[string]string{"en": "Test Notification"}
				contentData := map[string]interface{}{
					"en": map[string]interface{}{
						"active":         true,
						"title":          "Test Title",
						"message":        "Test Message",
						"key_image":      "image.jpg",
						"article_images": []string{"image1.jpg", "image2.jpg"},
					},
				}
				contentJSON := entities.NewJSON(contentData)

				mockRepo.On("GetNotificationById", ctx, notificationID).Return(&entities.Notification{
					ID:        &notificationID,
					CreatedAt: &now,
					UpdatedAt: &now,
					Type:      "IMPORTANT_SYSTEM",
					Title:     entities.NewJSON(titleData),
					Content:   pointer.Ptr(contentJSON),
				}, nil).Once()
			},
			want: &Notification{
				ID:        notificationID,
				CreatedAt: now,
				UpdatedAt: now,
				Type:      "IMPORTANT_SYSTEM",
				Title:     map[string]string{"en": "Test Notification"},
				Content: &map[string]NotificationArticleContent{
					"en": {
						Active:        true,
						Title:         "Test Title",
						Message:       "Test Message",
						KeyImage:      pointer.Ptr("image.jpg"),
						ArticleImages: []string{"image1.jpg", "image2.jpg"},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "ERROR - Repository error",
			id:   notificationID,
			mock: func() {
				mockRepo.On("GetNotificationById", ctx, notificationID).Return(nil, errors.New("database error")).Once()
			},
			want:    nil,
			wantErr: errors.New("get notification by id: database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()

			got, err := domain.GetNotificationById(ctx, tt.id)

			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.wantErr.Error())
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.want.ID, got.ID)
				assert.Equal(t, tt.want.Type, got.Type)
				assert.Equal(t, tt.want.Title, got.Title)
			}
		})
	}
}

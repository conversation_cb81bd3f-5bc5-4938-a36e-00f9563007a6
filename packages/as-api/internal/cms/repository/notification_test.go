package repository

import (
	"context"
	"testing"
	"time"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"
	"as-api/as/pkg/helpers/pointer"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRepository_GetNotifications(t *testing.T) {
	t.<PERSON>l()

	tests := []struct {
		name     string
		page     int
		limit    int
		mockFunc func(mock sqlmock.Sqlmock)
		want     struct {
			notifications []*entities.Notification
			total         int
			err           bool
		}
	}{
		{
			name:  "SUCCESS - Get notifications",
			page:  1,
			limit: 10,
			mockFunc: func(mock sqlmock.Sqlmock) {
				now := time.Now()
				rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "system_type", "type", "title", "message", "icon", "data", "content", "status", "published_at"}).
					AddRow("1", now, now, "system", "IMPORTANT_SYSTEM", `{"en":"Test Notification"}`, `{"en":"Test Message"}`, nil, `{"key":"value"}`, `{"en":{"active":true,"title":"Test Title","message":"Test Message","keyImage":"image.jpg","articleImages":["image1.jpg","image2.jpg"]}}`, "published", now)

				countRows := sqlmock.NewRows([]string{"count"}).
					AddRow(1)

				mock.ExpectQuery(`SELECT count\(\*\) FROM "notifications" WHERE "notifications"\."country_codes" IS NOT NULL AND "notifications"\."deleted_at" IS NULL`).
					WillReturnRows(countRows)

				mock.ExpectQuery(`SELECT \* FROM "notifications" WHERE "notifications"\."country_codes" IS NOT NULL AND "notifications"\."deleted_at" IS NULL`).
					WithArgs(10).
					WillReturnRows(rows)
			},
			want: struct {
				notifications []*entities.Notification
				total         int
				err           bool
			}{
				notifications: []*entities.Notification{
					{
						ID:   &[]string{"1"}[0],
						Type: "IMPORTANT_SYSTEM",
					},
				},
				total: 1,
				err:   false,
			},
		},
		{
			name:  "ERROR - Database error",
			page:  1,
			limit: 10,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT count\(\*\) FROM "notifications" WHERE "notifications"\."country_codes" IS NOT NULL AND "notifications"\."deleted_at" IS NULL`).
					WillReturnError(assert.AnError)
			},
			want: struct {
				notifications []*entities.Notification
				total         int
				err           bool
			}{
				notifications: nil,
				total:         0,
				err:           true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			m, err := factory.SetUpMock(nil)
			require.NoError(t, err)

			tt.mockFunc(m.Mock)

			// Create repository
			repo := NewCmsRepository(m.DO)

			// Call repository method
			notifications, total, err := repo.GetNotifications(context.Background(), map[string]any{}, tt.page, tt.limit)

			// Assertions
			if tt.want.err {
				assert.Error(t, err)
				assert.Nil(t, notifications)
				assert.Zero(t, total)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, notifications)
				assert.Len(t, notifications, len(tt.want.notifications))
				assert.Equal(t, tt.want.total, pointer.Safe(total))

				if len(tt.want.notifications) > 0 && len(notifications) > 0 {
					assert.Equal(t, *tt.want.notifications[0].ID, *notifications[0].ID)
					assert.Equal(t, tt.want.notifications[0].Type, notifications[0].Type)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, m.Mock.ExpectationsWereMet())
		})
	}
}

func TestRepository_GetNotificationById(t *testing.T) {
	t.Parallel()

	notificationID := "test-notification-id"

	tests := []struct {
		name     string
		id       string
		mockFunc func(mock sqlmock.Sqlmock)
		want     struct {
			notification *entities.Notification
			err          bool
		}
	}{
		{
			name: "SUCCESS - Get notification by id",
			id:   notificationID,
			mockFunc: func(mock sqlmock.Sqlmock) {
				now := time.Now()
				rows := sqlmock.NewRows([]string{"id", "created_at", "updated_at", "system_type", "type", "title", "message", "icon", "data", "content", "status", "published_at"}).
					AddRow(notificationID, now, now, "system", "IMPORTANT_SYSTEM", `{"en":"Test Notification"}`, `{"en":"Test Message"}`, nil, `{"key":"value"}`, `{"en":{"active":true,"title":"Test Title","message":"Test Message","keyImage":"image.jpg","articleImages":["image1.jpg","image2.jpg"]}}`, "published", now)

				mock.ExpectQuery(`SELECT \* FROM "notifications" WHERE "notifications"\."id" = \$1 AND "notifications"\."deleted_at" IS NULL ORDER BY "notifications"\."id" LIMIT \$2`).
					WithArgs(notificationID, 1).
					WillReturnRows(rows)
			},
			want: struct {
				notification *entities.Notification
				err          bool
			}{
				notification: &entities.Notification{
					ID:   &notificationID,
					Type: "IMPORTANT_SYSTEM",
				},
				err: false,
			},
		},
		{
			name: "ERROR - Notification not found",
			id:   "non-existent-id",
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(`SELECT \* FROM "notifications" WHERE "notifications"\."id" = \$1 AND "notifications"\."deleted_at" IS NULL ORDER BY "notifications"\."id" LIMIT \$2`).
					WithArgs("non-existent-id", 1).
					WillReturnError(assert.AnError)
			},
			want: struct {
				notification *entities.Notification
				err          bool
			}{
				notification: nil,
				err:          true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			m, err := factory.SetUpMock(nil)
			require.NoError(t, err)

			tt.mockFunc(m.Mock)

			// Create repository
			repo := NewCmsRepository(m.DO)

			// Call repository method
			notification, err := repo.GetNotificationById(context.Background(), tt.id)

			// Assertions
			if tt.want.err {
				assert.Error(t, err)
				assert.Nil(t, notification)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, notification)
				if notification != nil && notification.ID != nil {
					assert.Equal(t, *tt.want.notification.ID, *notification.ID)
					assert.Equal(t, tt.want.notification.Type, notification.Type)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, m.Mock.ExpectationsWereMet())
		})
	}
}

func TestRepository_CreateNotification(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		notification *entities.Notification
		mockFunc     func(mock sqlmock.Sqlmock)
		want         struct {
			notification *entities.Notification
			err          bool
		}
	}{
		{
			name: "SUCCESS - Create notification",
			notification: &entities.Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   entities.NewJSON(map[string]string{"en": "Test Notification"}),
				Message: entities.NewJSON(map[string]string{"en": "Test Message"}),
				Status:  &[]string{"draft"}[0],
			},
			mockFunc: func(mock sqlmock.Sqlmock) {
				now := time.Now()

				// Mock direct query without transaction
				mock.ExpectQuery(`INSERT INTO "notifications"`).
					WithArgs(
						sqlmock.AnyArg(),   // deleted_at
						sqlmock.AnyArg(),   // system_type
						"IMPORTANT_SYSTEM", // type
						sqlmock.AnyArg(),   // title
						sqlmock.AnyArg(),   // message
						sqlmock.AnyArg(),   // icon
						sqlmock.AnyArg(),   // data
						sqlmock.AnyArg(),   // country_codes
						"draft",            // status
						sqlmock.AnyArg(),   // published_at
						sqlmock.AnyArg(),   // group_id
					).
					WillReturnRows(sqlmock.NewRows([]string{"id", "created_at", "updated_at"}).
						AddRow("1", now, now))
			},
			want: struct {
				notification *entities.Notification
				err          bool
			}{
				notification: &entities.Notification{
					ID:   &[]string{"1"}[0],
					Type: "IMPORTANT_SYSTEM",
				},
				err: false,
			},
		},
		{
			name: "ERROR - Database error",
			notification: &entities.Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   entities.NewJSON(map[string]string{"en": "Test Notification"}),
				Message: entities.NewJSON(map[string]string{"en": "Test Message"}),
				Status:  &[]string{"draft"}[0],
			},
			mockFunc: func(mock sqlmock.Sqlmock) {
				// Mock direct query without transaction
				mock.ExpectQuery(`INSERT INTO "notifications"`).
					WithArgs(
						sqlmock.AnyArg(),   // deleted_at
						sqlmock.AnyArg(),   // system_type
						"IMPORTANT_SYSTEM", // type
						sqlmock.AnyArg(),   // title
						sqlmock.AnyArg(),   // message
						sqlmock.AnyArg(),   // icon
						sqlmock.AnyArg(),   // data
						sqlmock.AnyArg(),   // country_codes
						"draft",            // status
						sqlmock.AnyArg(),   // published_at
						sqlmock.AnyArg(),   // group_id
					).
					WillReturnError(assert.AnError)
			},
			want: struct {
				notification *entities.Notification
				err          bool
			}{
				notification: nil,
				err:          true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			m, err := factory.SetUpMock(nil)
			require.NoError(t, err)

			tt.mockFunc(m.Mock)

			// Create repository
			repo := NewCmsRepository(m.DO)

			// Call repository method
			notification, err := repo.CreateNotification(context.Background(), tt.notification)

			// Assertions
			if tt.want.err {
				assert.Error(t, err)
				assert.Nil(t, notification)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, notification)
				assert.NotNil(t, notification.ID)
				assert.Equal(t, *tt.want.notification.ID, *notification.ID)
				assert.Equal(t, tt.want.notification.Type, notification.Type)
			}

			// Verify all expectations were met
			assert.NoError(t, m.Mock.ExpectationsWereMet())
		})
	}
}

func TestRepository_UpdateNotification(t *testing.T) {
	t.Parallel()

	id := "notification-123"

	tests := []struct {
		name         string
		id           string
		notification *entities.Notification
		mockFunc     func(mock sqlmock.Sqlmock)
		want         struct {
			notification *entities.Notification
			err          bool
		}
	}{
		{
			name: "SUCCESS - Update notification",
			id:   id,
			notification: &entities.Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   entities.NewJSON(map[string]string{"en": "Updated Notification"}),
				Message: entities.NewJSON(map[string]string{"en": "Updated Message"}),
				Status:  &[]string{"published"}[0],
			},
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(`UPDATE "notifications" SET`).
					WithArgs(sqlmock.AnyArg(), "IMPORTANT_SYSTEM", sqlmock.AnyArg(), sqlmock.AnyArg(), "published", id, id).
					WillReturnResult(sqlmock.NewResult(1, 1))

				// The actual update just returns the notification object passed to the method
				// and doesn't perform a select query
			},
			want: struct {
				notification *entities.Notification
				err          bool
			}{
				notification: &entities.Notification{
					Type:    "IMPORTANT_SYSTEM",
					Title:   entities.NewJSON(map[string]string{"en": "Updated Notification"}),
					Message: entities.NewJSON(map[string]string{"en": "Updated Message"}),
					Status:  &[]string{"published"}[0],
				},
				err: false,
			},
		},
		{
			name: "ERROR - Database error",
			id:   id,
			notification: &entities.Notification{
				Type:    "IMPORTANT_SYSTEM",
				Title:   entities.NewJSON(map[string]string{"en": "Updated Notification"}),
				Message: entities.NewJSON(map[string]string{"en": "Updated Message"}),
				Status:  &[]string{"published"}[0],
			},
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(`UPDATE "notifications" SET`).
					WithArgs(sqlmock.AnyArg(), "IMPORTANT_SYSTEM", sqlmock.AnyArg(), sqlmock.AnyArg(), "published", id, id).
					WillReturnError(assert.AnError)
			},
			want: struct {
				notification *entities.Notification
				err          bool
			}{
				notification: nil,
				err:          true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			m, err := factory.SetUpMock(nil)
			require.NoError(t, err)

			tt.mockFunc(m.Mock)

			// Create repository
			repo := NewCmsRepository(m.DO)

			// Call repository method
			notification, err := repo.UpdateNotification(context.Background(), tt.id, tt.notification)

			// Assertions
			if tt.want.err {
				assert.Error(t, err)
				assert.Nil(t, notification)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, notification)
				assert.Equal(t, tt.notification.Type, notification.Type)

				if tt.want.notification.Status != nil && notification.Status != nil {
					assert.Equal(t, *tt.want.notification.Status, *notification.Status)
				}
			}

			// Verify all expectations were met
			assert.NoError(t, m.Mock.ExpectationsWereMet())
		})
	}
}

func TestRepository_DeleteNotification(t *testing.T) {
	t.Parallel()

	id := "notification-123"

	tests := []struct {
		name     string
		id       string
		mockFunc func(mock sqlmock.Sqlmock)
		wantErr  bool
	}{
		{
			name: "SUCCESS - Delete notification",
			id:   id,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(`UPDATE "notifications" SET "deleted_at"=\$1 WHERE "notifications"\."id" = \$2`).
					WithArgs(sqlmock.AnyArg(), id).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			wantErr: false,
		},
		{
			name: "ERROR - Database error",
			id:   id,
			mockFunc: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(`UPDATE "notifications" SET "deleted_at"=\$1 WHERE "notifications"\."id" = \$2`).
					WithArgs(sqlmock.AnyArg(), id).
					WillReturnError(assert.AnError)
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock
			m, err := factory.SetUpMock(nil)
			require.NoError(t, err)

			tt.mockFunc(m.Mock)

			// Create repository
			repo := NewCmsRepository(m.DO)

			// Call repository method
			err = repo.DeleteNotification(context.Background(), tt.id)

			// Assertions
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			// Verify all expectations were met
			assert.NoError(t, m.Mock.ExpectationsWereMet())
		})
	}
}

package cms

import (
	"fmt"

	"as-api/as/foundations/db/entities"
	"as-api/as/internal/cms/model"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

func ParseContentCategoryFromModel(cc *model.ContentCategory) (*ContentCategory, error) {
	if cc.DisplayName == nil {
		return nil, fmt.Errorf("display name is nil")
	}

	if cc.DisplayName.Data == nil {
		return nil, fmt.Errorf("invalid display name JSON: data is nil")
	}

	displayName, err := entities.ConvertJSONToData[map[string]string](cc.DisplayName)
	if err != nil {
		return nil, errors.Wrap(err, "convert display name to data")
	}

	ct, err := ParseContentTypeFromEntity(&cc.ContentType)
	if err != nil {
		return nil, errors.Wrap(err, "parse content type from entity")
	}

	return &ContentCategory{
		Code:        cc.Code,
		DisplayName: pointer.Safe(displayName),
		ContentType: *ct,
	}, nil
}

func ParseContentTypeFromEntity(ct *entities.ContentType) (*ContentType, error) {
	// Check if the JSON is invalid
	if ct.Fields.Data == nil {
		return nil, fmt.Errorf("invalid fields JSON: data is nil")
	}

	// Ensure marshaling works without errors
	_, err := ct.Fields.MarshalJSON()
	if err != nil {
		return nil, fmt.Errorf("invalid fields JSON format: %w", err)
	}

	fields, err := entities.ConvertJSONToData[[]Field](&ct.Fields)
	if err != nil {
		return nil, errors.Wrap(err, "convert fields to data")
	}

	return &ContentType{
		Code:   ct.Code,
		Fields: pointer.Safe(fields),
	}, nil
}

func ParseContentFromModel(c *model.Content) (*Content, error) {
	content, err := entities.ConvertJSONToData[map[string]*ContentContent](c.Content)
	if err != nil {
		return nil, errors.Wrap(err, "convert content to data")
	}

	title, err := entities.ConvertJSONToData[map[string]string](&c.Title)
	if err != nil {
		return nil, errors.Wrap(err, "convert title to data")
	}

	return &Content{
		ID:                  c.ID,
		Title:               pointer.Safe(title),
		Content:             pointer.Safe(content),
		CountryCode:         c.CountryCode,
		ContentCategoryCode: c.ContentCategoryCode,
		ParentContentID:     c.ParentContentID,
		PublishedAt:         c.PublishedAt,
		Status:              c.Status,
		CreatedBy:           ParseAdminFromEntity(c.CreatedBy),
		CreatedAt:           c.CreatedAt,
		UpdatedBy:           ParseAdminFromEntity(c.UpdatedBy),
		UpdatedAt:           c.UpdatedAt,
	}, nil
}

func ParseContentToEntity(c *Content, updatedBy *string) (*entities.Content, error) {
	content, err := entities.ConvertDataToJSON(&c.Content)
	if err != nil {
		return nil, err
	}

	title, err := entities.ConvertDataToJSON(&c.Title)
	if err != nil {
		return nil, err
	}

	results := &entities.Content{
		Title:               pointer.Safe(title),
		Content:             content,
		CountryCode:         c.CountryCode,
		ContentCategoryCode: c.ContentCategoryCode,
		ParentContentID:     c.ParentContentID,
		PublishedAt:         c.PublishedAt,
		Status:              c.Status,
		UpdatedBy:           pointer.Safe(updatedBy),
	}

	if c.ID == nil {
		results.CreatedBy = pointer.Safe(updatedBy)
	}

	return results, nil
}

func ParseAdminFromEntity(a entities.Admin) *Admin {
	return &Admin{
		ID:   pointer.Safe(a.ID),
		Name: a.Name,
	}
}

func ParseLanguageFromModel(l *model.Language) (*Language, error) {
	if l.DisplayName == nil {
		return nil, fmt.Errorf("display name is nil")
	}

	if l.DisplayName.Data == nil {
		return nil, fmt.Errorf("invalid display name JSON: data is nil")
	}

	displayName, err := entities.ConvertJSONToData[map[string]string](l.DisplayName)
	if err != nil {
		return nil, errors.Wrap(err, "convert display name to data")
	}

	return &Language{
		Code:        l.Code,
		DisplayName: pointer.Safe(displayName),
		Active:      l.Active,
	}, nil
}

func ParseCountryFromModel(c *model.Country) (*Country, error) {
	displayName, err := entities.ConvertJSONToData[map[string]string](c.DisplayName)
	if err != nil {
		return nil, errors.Wrap(err, "convert display name to data")
	}

	mRegions := make(map[string]*Region, len(c.Regions))
	for _, r := range c.Regions {
		region, err := ParseRegionFromEntity(r)
		if err != nil {
			return nil, errors.Wrap(err, "parse region from entity")
		}
		mRegions[region.Id] = region
	}

	regions := make([]*Region, 0, len(c.Regions))
	for _, r := range c.Regions {
		region, ok := mRegions[pointer.Safe(r.ID)]
		if !ok {
			continue
		}

		if r.ParentID == nil {
			regions = append(regions, region)
			continue
		}

		parentRegion, ok := mRegions[pointer.Safe(r.ParentID)]
		if !ok {
			continue
		}
		parentRegion.SubRegions = append(parentRegion.SubRegions, region)
	}

	currencies, err := entities.ConvertJSONToData[[]*Currency](c.Currencies)
	if err != nil {
		return nil, errors.Wrap(err, "convert currencies to data")
	}

	country := &Country{
		Code:            c.Code,
		DisplayName:     pointer.Safe(displayName),
		Regions:         regions,
		PhoneCode:       c.PhoneCode,
		Currencies:      pointer.Safe(currencies),
		IsCorporateOnly: pointer.Safe(c.IsCorporateOnly),
		BankAccountType: c.BankAccountType,
		Active:          c.Active,
	}

	if c.Code != CountryCodeUSA {
		return country, nil
	}

	for _, r := range regions {
		var l int
		for _, sr := range r.SubRegions {
			l += len(sr.SubRegions)
		}

		cityRegions := make([]*Region, 0, l)
		for _, sr := range r.SubRegions {
			cityRegions = append(cityRegions, sr.SubRegions...)
		}
		r.SubRegions = cityRegions
	}

	return country, nil
}

func ParseRegionFromEntity(r *entities.Region) (*Region, error) {
	displayName, err := entities.ConvertJSONToData[map[string]string](&r.DisplayName)
	if err != nil {
		return nil, errors.Wrap(err, "convert display name to data")
	}

	return &Region{
		Id:          pointer.Safe(r.ID),
		DisplayName: pointer.Safe(displayName),
	}, nil
}

func ParseNotificationToEntity(req *Notification) (*entities.Notification, error) {
	contentJSON, err := entities.ConvertDataToJSON(req.Content)
	if err != nil {
		return nil, err
	}

	content := pointer.Safe(req.Content)
	title := make(map[string]string, len(content))
	message := make(map[string]string, len(content))
	for k, v := range content {
		if !v.Active {
			continue
		}

		title[k] = v.Title
		message[k] = v.Message
	}

	// Convert map[string]string to entities.JSON for Title
	titleJSON, err := entities.ConvertDataToJSON(&title)
	if err != nil {
		return nil, err
	}

	// Convert map[string]string to entities.JSON for Message
	messageJSON, err := entities.ConvertDataToJSON(&message)
	if err != nil {
		return nil, err
	}

	// Create entity with proper types
	entity := &entities.Notification{
		SystemType: req.SystemType,
		Type:       req.Type,
		Title:      pointer.Safe(titleJSON),
		Message:    pointer.Safe(messageJSON),
		Content:    contentJSON,
	}

	if len(req.Countries) > 0 {
		entity.CountryCodes = pointer.Ptr[entities.StringArray](req.Countries)
	}

	// Convert status string to *string
	entity.Status = pointer.Ptr(req.Status)

	if req.Icon != nil {
		entity.Icon = req.Icon
	}

	if req.Data != nil {
		dataJSON := entities.JSON{Data: *req.Data}
		entity.Data = &dataJSON
	}

	if req.PublishedAt != nil {
		entity.PublishedAt = req.PublishedAt
	}

	if req.Group != nil {
		entity.GroupID = req.Group
	}

	return entity, nil
}

func ParseNotificationFromEntity(n *entities.Notification) (*Notification, error) {
	notification := &Notification{
		ID:          pointer.Safe(n.ID),
		CreatedAt:   pointer.Safe(n.CreatedAt),
		UpdatedAt:   pointer.Safe(n.UpdatedAt),
		Status:      pointer.Safe(n.Status),
		PublishedAt: n.PublishedAt,
		Icon:        n.Icon,
		SystemType:  n.SystemType,
		Type:        n.Type,
		Group:       n.GroupID,
	}

	if countries := pointer.Safe(n.CountryCodes); len(countries) > 0 {
		notification.Countries = countries
	}

	// Convert Title JSON to map[string]string
	title, err := entities.ConvertJSONToData[map[string]string](&n.Title)
	if err != nil {
		return nil, errors.Wrap(err, "convert title to data")
	}
	notification.Title = pointer.Safe(title)

	// Convert Message JSON to map[string]string
	message, err := entities.ConvertJSONToData[map[string]string](&n.Message)
	if err != nil {
		return nil, errors.Wrap(err, "convert message to data")
	}
	notification.Message = pointer.Safe(message)

	content, err := entities.ConvertJSONToData[map[string]NotificationArticleContent](n.Content)
	if err != nil {
		return nil, errors.Wrap(err, "convert content to data")
	}
	notification.Content = content

	// Convert Data JSON to map[string]string if not nil
	data, err := entities.ConvertJSONToData[map[string]any](n.Data)
	if err != nil {
		return nil, errors.Wrap(err, "convert data to data")
	}
	notification.Data = data

	return notification, nil
}

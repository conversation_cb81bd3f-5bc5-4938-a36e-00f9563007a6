package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"time"

	"as-api/as/api"
	"as-api/as/docs"
	"as-api/as/event"
	"as-api/as/foundations/db/factory"
	"as-api/as/foundations/env"
	"as-api/as/foundations/env/aws"
	"as-api/as/foundations/env/files"
	"as-api/as/foundations/gmoapi"
	"as-api/as/foundations/logger"
	"as-api/as/foundations/middleware"
	"as-api/as/foundations/paypalapi"
	"as-api/as/pkg/awss3"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/email"
	"as-api/as/pkg/helpers/subscription"
	"as-api/as/pkg/slack"
	"as-api/as/pkg/validator"

	"github.com/aws/aws-sdk-go/aws/client"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/valve"
	"github.com/pkg/errors"
	"go.uber.org/dig"
)

type App struct {
	dig.In

	Handler      api.HTTPHandler
	Env          env.MapperData
	Logger       logger.Logger
	EventService event.EventService
}

func (a App) setupEvents() {
	// Setup all event handlers using the event service
	a.EventService.SetupHandlers()
}

func (a App) router() (*chi.Mux, error) {
	r := chi.NewRouter()

	// apply middleware
	if err := middleware.Use(r, a.Env, a.Logger); err != nil {
		return nil, errors.Wrap(err, "failed initialize middleware")
	}

	r.Get("/", func(w http.ResponseWriter, r *http.Request) {
		if _, err := w.Write([]byte(http.StatusText(http.StatusOK))); err != nil {
			w.WriteHeader(http.StatusInternalServerError)
		}
	})

	if os.Getenv("ENV") != "production" {
		r.Mount("/debug", middleware.Profiler())

		docs.ExportDocs(r)
	}

	api.BuildRoute(a.Handler, r, a.Env, a.Logger)

	// Register the 404 (Not Found) handler to handle unmatched routes.
	r.NotFound(func(w http.ResponseWriter, r *http.Request) {
		response := apiutil.NewJSONResponse(w, r, a.Logger)
		response.Failure(errors.Wrap(apiutil.ErrPermissionDenied, "not found route"))
	})

	return r, nil
}

// All pending processes (web request, loops) are completed - no new processes should start and no new web requests should be accepted.
// Closing all open connections to external services and databases.
func (a App) hookShutdown(c chan os.Signal, srv *http.Server, valv *valve.Valve) {
	go func() {
		timeout := 30 * time.Second
		for range c {
			func() {
				if err := valv.Shutdown(timeout); err != nil {
					a.Logger.Errorf("valv shutdown err: " + err.Error())
				}
				ctx, cancel := context.WithTimeout(context.Background(), timeout)
				defer cancel()
				if err := srv.Shutdown(ctx); err != nil {
					a.Logger.Errorf("server shutdown err:" + err.Error())
				}
				a.Logger.Info("server shutdown")
				select {
				case <-time.After(timeout + 1):
					a.Logger.Info("not all connections done")
				case <-ctx.Done():
				}
			}()
		}
	}()
}

func (a App) Run() error {
	// Setup event handlers
	a.setupEvents()

	// init router
	r, err := a.router()
	if err != nil {
		return errors.Wrap(err, "failed initialize router")
	}

	// default port 3000 if it is not defined in env
	p := os.Getenv("PORT")
	if len(p) == 0 {
		p = "3000"
	}

	// init valve
	valv := valve.New()
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%s", p),
		Handler: r,
	}
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)

	a.hookShutdown(c, srv, valv)

	if err := srv.ListenAndServe(); err != nil {
		if err != http.ErrServerClosed {
			return errors.Wrap(err, "an unexpected error has occurred")
		}
	}
	return nil
}

func setup() error {
	// slack notifier
	if err := di.RegisterProviders(logger.NotifierProvider[slack.SlackService]); err != nil {
		return errors.Wrap(err, "register constructor logger notifier provider failed")
	}

	// logger
	if err := di.RegisterProviders(logger.NewLoggerWithNotifier); err != nil {
		return errors.Wrap(err, "register constructor logger failed")
	}

	// validator
	if err := di.RegisterProviders(validator.NewValidator); err != nil {
		return errors.Wrap(err, "register constructor validator failed")
	}

	// env
	if os.Getenv("ENV") == "develop" {
		if err := di.RegisterProviders(files.New); err != nil {
			return errors.Wrap(err, "register constructor env failed")
		}
	} else {
		if err := di.RegisterProviders(aws.New); err != nil {
			return errors.Wrap(err, "register constructor env failed")
		}
	}
	if err := di.RegisterProviders(env.New); err != nil {
		return errors.Wrap(err, "register constructor env failed")
	}
	if err := di.RegisterProviders(env.ENV.Load); err != nil {
		return errors.Wrap(err, "load env failed")
	}

	// DB
	if err := di.RegisterProviders(configDB); err != nil {
		return errors.Wrap(err, "load config db failed")
	}
	if err := di.RegisterProviders(factory.NewConnection); err != nil {
		return errors.Wrap(err, "load db manager failed")
	}
	if err := di.RegisterProviders(factory.NewDO); err != nil {
		return errors.Wrap(err, "load do failed")
	}

	// Email
	if err := di.RegisterProviders(configEmail); err != nil {
		return errors.Wrap(err, "load config email failed")
	}
	if err := di.RegisterProviders(email.New); err != nil {
		return errors.Wrap(err, "load email service failed")
	}

	// AWS
	if err := di.RegisterProviders(configAWS); err != nil {
		return errors.Wrap(err, "load config aws failed")
	}

	if err := di.RegisterProviders(awss3.NewS3Service); err != nil {
		return errors.Wrap(err, "register constructor s3 service failed")
	}

	// Subscription
	if err := di.RegisterProviders(configSubscription); err != nil {
		return errors.Wrap(err, "load config subscription failed")
	}
	if err := di.RegisterProviders(subscription.New); err != nil {
		return errors.Wrap(err, "failed to register subscription dependencies")
	}

	// GMO
	if err := di.RegisterProviders(configGMO); err != nil {
		return errors.Wrap(err, "load config gmo failed")
	}
	if err := di.RegisterProviders(gmoapi.NewGMOClient); err != nil {
		return errors.Wrap(err, "load gmo client failed")
	}

	// PayPal
	if err := di.RegisterProviders(configPayPal); err != nil {
		return errors.Wrap(err, "load config paypal failed")
	}

	if err := di.RegisterProviders(paypalapi.NewPayPalClient); err != nil {
		return errors.Wrap(err, "load paypal client failed")
	}

	// Maintenance Checker
	if err := di.RegisterProviders(middleware.NewMaintenanceChecker); err != nil {
		return errors.Wrap(err, "register constructor maintenance checker failed")
	}

	// User Status Checker
	if err := di.RegisterProviders(middleware.NewUserStatusChecker); err != nil {
		return errors.Wrap(err, "register constructor user status checker failed")
	}

	// Country Status Checker
	if err := di.RegisterProviders(middleware.NewCountryStatusChecker); err != nil {
		return errors.Wrap(err, "register constructor country status checker failed")
	}

	return nil
}

func configDB(e env.MapperData) factory.Config {
	return factory.Config{
		User:     e.PostgresUser,
		Password: e.PostgresPassword,
		Host:     e.PostgresHostname,
		Database: e.PostgresDatabase,
		Port:     e.PostgresPort,
	}
}

func configEmail(e env.MapperData) email.Config {
	return email.Config{
		Host:     e.SMTPHost,
		Port:     e.SMTPPort,
		Username: e.SMTPUsername,
		Password: e.SMTPPassword,
		From:     e.SMTPFrom,
	}
}

func configAWS(e env.MapperData) client.ConfigProvider {
	sess, err := aws.GetSession()
	if err != nil {
		panic(err.Error())
	}

	return sess
}

func configSubscription(e env.MapperData) subscription.Config {
	return subscription.Config{
		PackageName:       e.PackageName,
		ServiceAccountKey: e.GoogleSubscriptionServiceAccount,
		AppleVerifyURL:    e.AppleVerifyURL,
		AppleSharedSecret: e.AppleSharedSecret,
		AppleRootCert:     e.AppleRootCert,
	}
}

func configGMO(e env.MapperData) gmoapi.GMOConfig {
	return gmoapi.GMOConfig{
		BaseURL:      e.GMO_BASE_URL,
		ShopID:       e.GMO_SHOP_ID,
		ShopPassword: e.GMO_SHOP_PASSWORD,
	}
}

func configPayPal(e env.MapperData) paypalapi.PayPalConfig {
	return paypalapi.PayPalConfig{
		BaseURL:      e.PayPalBaseURL,
		ClientID:     e.PayPalClientID,
		ClientSecret: e.PayPalClientSecret,
	}
}

func main() {
	// setup providers
	if err := setup(); err != nil {
		log.Fatal("register common providers:", err)
	}

	// run app
	if err := di.Run(App.Run); err != nil {
		log.Fatal("run failed:", err)
	}
}

.PHONY: install format test clean build run

# Set default env as develop.
ifeq ($(ENV),)
ENV := develop
endif

# Set current Git revision unless given explicitly.
ifeq ($(GIT_REVISION),)
GIT_REVISION := $(shell git rev-parse --short HEAD)
endif

BUILD := ./build

API_DOC_DIR := ./docs
API_DOC := $(API_DOC_DIR)/api.$(ROLE).yml

DAO_DIR := ./database/dao

GORUN := GIT_REVISION=$(GIT_REVISION) go run

define _build
	$(eval target := $(abspath $(strip $1)))
	$(eval output := $(abspath $(BUILD)/$(strip $2)))

	@echo "Compiling $(target) --> $(output)"
	cd $(target) && GOOS=linux GOARCH=amd64 go build -o $(output) $(target)
	@echo "Done!\n"
endef

TEST := GIT_REVISION=$(GIT_REVISION) ENV=test go test -cover -coverprofile coverage.log

TEST_TOOL := go tool cover -html=coverage.log

DB_GEN := cd cmd/dbgen/ && go run .

MIGRATE := cd cmd/migrate/ && go run .

WORKER_DIR := cmd/cronjobs

define _apigen
	$(eval package_name := $(subst -,,$(1)))
	$(eval file_name := $(subst -,_,$(1)))
	oapi-codegen -generate chi-server -include-tags="$(1)" -package="$(package_name)" $(API_DOC) > "api/$(ROLE)/$(1)/$(file_name).gen.go"
endef

define _apigen_w_query
	arg=$(1) oapi-codegen -config="docs/cfg.yml" $(API_DOC)
endef

define _genconfig
	$(eval package_name := $(subst -,,$(1)))
	$(eval file_name := $(subst -,_,$(1)))
	echo "" > $(API_DOC_DIR)/cfg.yml
	echo "package: $(package_name)" >> $(API_DOC_DIR)/cfg.yml
	echo "generate:" >> $(API_DOC_DIR)/cfg.yml
	echo "  chi-server: true" >> $(API_DOC_DIR)/cfg.yml
	echo "output-options:" >> $(API_DOC_DIR)/cfg.yml
	echo "  include-tags:" >> $(API_DOC_DIR)/cfg.yml
	echo "    - $(1)" >> $(API_DOC_DIR)/cfg.yml
	echo "additional-imports:" >> $(API_DOC_DIR)/cfg.yml
	echo "  - alias: ." >> $(API_DOC_DIR)/cfg.yml
	echo "    package: as-api/as/dtos/$(ROLE)" >> $(API_DOC_DIR)/cfg.yml
	echo "output: api/$(ROLE)/$(1)/$(file_name).gen.go" >> $(API_DOC_DIR)/cfg.yml
endef

define _paypalapigen
	$(eval file_name := $(subst -,_,$(1)))
	oapi-codegen -config="$(API_DOC_DIR)/cfg.yml" foundations/paypalapi/$(1)/$(file_name).yml > "foundations/paypalapi/$(1)/$(file_name).gen.go"
endef

define _paypalapigenconfig
	$(eval package_name := $(subst -,,$(1)))	
	$(eval file_name := $(subst -,_,$(1)))
	echo "package: $(package_name)" > $(API_DOC_DIR)/cfg.yml
	echo "output: foundations/paypalapi/$(1)/$(file_name).gen.go" >> $(API_DOC_DIR)/cfg.yml
	echo "generate:" >> $(API_DOC_DIR)/cfg.yml
	echo "  models: true" >> $(API_DOC_DIR)/cfg.yml
	echo "  client: true" >> $(API_DOC_DIR)/cfg.yml
	echo "output-options:" >> $(API_DOC_DIR)/cfg.yml
	echo "  skip-prune: true" >> $(API_DOC_DIR)/cfg.yml
	echo "  response-type-suffix: Resp" >> $(API_DOC_DIR)/cfg.yml
endef

define _mock
    mockery --dir $(1) --all --inpackage --inpackage-suffix -r --case underscore $(2)
endef

run:
	air

worker-run:
	cd $(WORKER_DIR) && go run ./...

format:
	go fmt ./...

dbgen:
	@echo "Generating entity models, DAO from database"
	$(DB_GEN)
	@echo "Generate completed"

# Copy api doc to be used with codegen.
api-doc:
	@echo "Copying api doc.."
	@mkdir -p $(API_DOC_DIR)
	@\cp -rf ../schema/build/$(ROLE)/index.yaml ./$(API_DOC)
	@echo "Success!"

dtogen:
	@echo "Start modelgen..."
	@mkdir -p dtos/$(ROLE)
	oapi-codegen -generate "types,skip-prune" -package="dtos" $(API_DOC) > "dtos/$(ROLE)/dto.gen.go"
	@echo "Success codegen! Check it out dtos/$(ROLE)/dto.gen.go"

apigen-help:
	@echo ""
	@echo "  Syntax:"
	@echo "  make apigen tag=<api tag> query=1 ROLE=<role>"
	@echo ""
	@echo "  Example:"
	@echo "  make apigen tag=health"
	@echo "  If query=1, that means the gen will imports dto package to apigen"
	@echo ""

apigen:
	@[ "${tag}" ] || ( make apigen-help ; exit 1 )
	@echo "Start apigen $(tag) ..."
	mkdir -p api/$(ROLE)/$(tag)
	@echo "Generate yml config file to $(API_DOC_DIR)"
	@$(call _genconfig,$(tag))
ifeq ($(query),1)
	@$(call _apigen_w_query,$(tag))
else
	@$(call _apigen,$(tag))
endif
	@echo "Success generate API! Check it out api/$(ROLE)/$(tag)/$(tag).gen.go"

paypalapigen:
	@[ "${tag}" ] || ( make paypalapigen-help ; exit 1 )
	$(eval file_name := $(subst -,_,$(tag)))
	@echo "Start paypalapigen $(tag) ..."
	mkdir -p foundations/paypalapi/$(tag)
	@echo "Generate yml config file to $(API_DOC_DIR)"
	@$(call _paypalapigenconfig,$(tag))
	@$(call _paypalapigen,$(tag))
	@echo "Success generate API! Check it out foundations/paypalapi/$(tag)/$(file_name).gen.go"

test:
	$(TEST) ./${dir}... && $(TEST_TOOL)

install:
	go mod download
	npm install -g swagger-cli

clean:
	rm -Rf $(BUILD)/

build: clean
	@$(call _build, "cmd/api", api)

build-migrate:
	@$(call _build, "cmd/migrate", migrate)

build-worker:
	@$(call _build, "cmd/cronjobs", worker)

build-yml-user:
	@echo "Building yml user..."
	cd /packages/schema && make build-yml-user

build-yml-admin:
	@echo "Building yml admin..."
	cd /packages/schema && make build-yml-admin

build-schema: build-yml-user build-yml-admin

migrate:
	@echo "Migrating..."
	$(MIGRATE)
	@echo "Done!"

mock-services:
	@$(call _mock, ./api/${ROLE}/${tag}/domain, --keeptree --output ./mocks/services/${ROLE}/${tag})

mock-pkgs:
ifeq ($(tag),validator)
	@$(call _mock, ./pkg/validator, --keeptree --output ./mocks/validator)
else ifeq ($(tag),awss3)
	@$(call _mock, ./pkg/awss3, --keeptree --output ./mocks/mockawss3)
else
	@$(call _mock, ./pkg/${tag}, --keeptree --output ./mocks/pkg/${tag})
endif

mock-foundations:
	@$(call _mock, ./foundations/${tag}, --keeptree --output ./mocks/foundations/${tag})

mock-domains:
	@$(call _mock, ./internal/${tag}, --keeptree --output ./mocks/domains/${tag})

mock-validator:
	@$(call _mock, ./pkg/validator, --keeptree --output ./mocks/validator)

mock-awss3:
	@$(call _mock, ./pkg/awss3, --keeptree --output ./mocks/mockawss3)

mock-event:
	@$(call _mock, ./event, --keeptree --output ./mocks/event)

package apiutil

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"as-api/as/foundations/logger"
	"as-api/as/pkg/context"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

type JSONResponse interface {
	ProcessJSONRequestBody(req interface{}) error

	Success(status int, response interface{}) error
	Failure(err error)
}

type jsonResponse struct {
	request *http.Request
	w       http.ResponseWriter
	logger  logger.Logger
}

func NewJSONResponse(w http.ResponseWriter, r *http.Request, log logger.Logger) JSONResponse {
	w.Header().Add("Content-Type", "application/json; charset=utf-8")
	return &jsonResponse{
		request: r,
		w:       w,
		logger:  log,
	}
}

func (v *jsonResponse) Success(status int, response interface{}) error {
	statusCode, body, err := responseNil(status, response)
	if err != nil {
		return fmt.Errorf("apiutil: failed to marshal response: %w", err)
	}

	v.w.<PERSON>(statusCode)
	if body != nil {
		if _, err := v.w.Write(body); err != nil {
			return fmt.Errorf("apiutil: failed to write body: %w", err)
		}
	}

	return nil
}

func responseNil(status int, response interface{}) (int, []byte, error) {
	body, err := json.Marshal(response)
	if err != nil {
		return status, nil, errors.Wrap(err, "apiutil: failed to marshal response")
	}

	if string(body) != "null" {
		return status, body, nil
	}

	if string(body) == "null" && status == http.StatusOK {
		return http.StatusNoContent, nil, nil
	}

	return status, nil, nil
}

func (v *jsonResponse) Failure(e error) {
	statusCode, err := toStatusCode(e)
	errCode := strconv.Itoa(statusCode)
	msg := err.Error()
	if resErr, ok := err.(*ResponseErr); ok {
		errCode = resErr.Code
		msg = resErr.Msg()
	}

	if statusCode == http.StatusInternalServerError {
		v.logger.Error(e.Error(), logger.FieldMap{
			"request_id": context.GetReqID(v.request.Context()),
		})
	} else {
		v.logger.Info(e.Error())
	}

	body, err := NewBaseError(errCode, msg, pointer.Ptr(e.Error()), pointer.Ptr(context.GetReqID(v.request.Context()))).toResponse()
	if err != nil {
		v.logger.Error(err.Error())
	}

	v.w.WriteHeader(statusCode)
	if _, err := v.w.Write(body); err != nil {
		v.logger.Error(err.Error())
	}
}

func (v *jsonResponse) ProcessJSONRequestBody(req interface{}) error {
	if v.request.Body == nil || v.request.Body == http.NoBody {
		return ErrInvalidRequest
	}

	if err := json.NewDecoder(v.request.Body).Decode(req); err != nil {
		return errors.Wrap(ErrInvalidRequest, err.Error())
	}

	return nil
}

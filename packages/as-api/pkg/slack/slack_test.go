package slack

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"as-api/as/foundations/env"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewSlackService(t *testing.T) {
	tests := []struct {
		name        string
		env         env.MapperData
		expectError bool
	}{
		{
			name: "successful creation with enabled Slack",
			env: env.MapperData{
				SecretData: env.SecretData{
					SlackWebhookURL: "https://hooks.slack.com/services/test",
					SlackChannel:    "#test-channel",
				},
			},
			expectError: false,
		},
		{
			name: "successful creation with disabled Slack",
			env: env.MapperData{
				SecretData: env.SecretData{},
			},
			expectError: false,
		},
		{
			name: "successful creation with no webhook URL",
			env: env.MapperData{
				SecretData: env.SecretData{},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := NewSlackService(tt.env)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, service)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
			}
		})
	}
}

func TestSlackService_SendErrorNotification(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "POST", r.Method)
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

		var msg SlackMessage
		err := json.NewDecoder(r.Body).Decode(&msg)
		require.NoError(t, err)

		assert.Equal(t, "#test-channel", msg.Channel)
		assert.Len(t, msg.Attachments, 1)

		attachment := msg.Attachments[0]
		assert.Equal(t, "danger", attachment.Color)
		assert.Equal(t, "🚨 TEST: Error Alert", attachment.Title)
		assert.Equal(t, "Test error message", attachment.Text)
		assert.Equal(t, "AS API Service", attachment.Footer)

		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	env := env.MapperData{
		SecretData: env.SecretData{
			SlackWebhookURL: server.URL,
			SlackChannel:    "#test-channel",
		},
	}

	service, err := NewSlackService(env)
	require.NoError(t, err)

	ctx := context.Background()
	err = service.SendErrorNotification(ctx, "Test error message", map[string]interface{}{
		"user_id": "123",
		"action":  "test_action",
	})

	assert.NoError(t, err)
}

func TestSlackService_SendErrorNotification_Disabled(t *testing.T) {
	env := env.MapperData{
		SecretData: env.SecretData{
			SlackWebhookURL: "https://hooks.slack.com/services/test",
			SlackChannel:    "#test-channel",
		},
	}

	service, err := NewSlackService(env)
	require.NoError(t, err)

	ctx := context.Background()
	err = service.SendErrorNotification(ctx, "Test error message", nil)

	assert.NoError(t, err)
}

func TestSlackService_SendMessage(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		assert.Equal(t, "POST", r.Method)
		assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

		var msg SlackMessage
		err := json.NewDecoder(r.Body).Decode(&msg)
		require.NoError(t, err)

		assert.Equal(t, "#test-channel", msg.Channel)
		assert.Equal(t, "Test message", msg.Text)

		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	env := env.MapperData{
		SecretData: env.SecretData{
			SlackWebhookURL: server.URL,
			SlackChannel:    "#test-channel",
		},
	}

	service, err := NewSlackService(env)
	require.NoError(t, err)

	ctx := context.Background()
	msg := SlackMessage{
		Channel: "#test-channel",
		Text:    "Test message",
	}

	err = service.SendMessage(ctx, msg)
	assert.NoError(t, err)
}

func TestSlackService_SendMessage_Error(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	}))
	defer server.Close()

	env := env.MapperData{
		SecretData: env.SecretData{
			SlackWebhookURL: server.URL,
			SlackChannel:    "#test-channel",
		},
	}

	service, err := NewSlackService(env)
	require.NoError(t, err)

	ctx := context.Background()
	msg := SlackMessage{
		Channel: "#test-channel",
		Text:    "Test message",
	}

	err = service.SendMessage(ctx, msg)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "status code: 500")
}

func TestSlackService_Timeout(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second)
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	env := env.MapperData{
		SecretData: env.SecretData{
			SlackWebhookURL: server.URL,
			SlackChannel:    "#test-channel",
		},
	}

	service, err := NewSlackService(env)
	require.NoError(t, err)

	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	msg := SlackMessage{
		Channel: "#test-channel",
		Text:    "Test message",
	}

	err = service.SendMessage(ctx, msg)
	assert.Error(t, err)
	// The error could be either timeout or context deadline exceeded
	assert.True(t,
		strings.Contains(err.Error(), "timeout") ||
			strings.Contains(err.Error(), "deadline exceeded") ||
			strings.Contains(err.Error(), "context canceled"),
		"Expected timeout error, got: %v", err)
}

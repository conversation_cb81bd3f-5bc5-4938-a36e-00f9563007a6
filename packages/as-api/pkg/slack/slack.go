package slack

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"as-api/as/foundations/env"

	"github.com/pkg/errors"
)

type SlackConfig struct {
	WebhookURL string
	Channel    string
}

type SlackMessage struct {
	Channel     string            `json:"channel,omitempty"`
	Text        string            `json:"text,omitempty"`
	Attachments []SlackAttachment `json:"attachments,omitempty"`
}

type SlackAttachment struct {
	Color      string       `json:"color,omitempty"`
	Title      string       `json:"title,omitempty"`
	Text       string       `json:"text,omitempty"`
	Fields     []SlackField `json:"fields,omitempty"`
	Timestamp  int64        `json:"ts,omitempty"`
	Footer     string       `json:"footer,omitempty"`
	FooterIcon string       `json:"footer_icon,omitempty"`
}

type SlackField struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

type SlackService interface {
	SendErrorNotification(ctx context.Context, message string, fields map[string]interface{}) error
	SendMessage(ctx context.Context, msg SlackMessage) error
}

type slackService struct {
	config SlackConfig
	client *http.Client
}

func NewSlackService(env env.MapperData) (SlackService, error) {
	config := SlackConfig{
		WebhookURL: env.SlackWebhookURL,
		Channel:    env.SlackChannel,
	}

	return &slackService{
		config: config,
		client: &http.Client{Timeout: 10 * time.Second},
	}, nil
}

func (s *slackService) SendErrorNotification(ctx context.Context, message string, fields map[string]interface{}) error {
	if s.config.WebhookURL == "" {
		return nil
	}

	attachment := SlackAttachment{
		Color:      "danger",
		Title:      "🚨 " + strings.ToUpper(os.Getenv("ENV")) + ": Error Alert",
		Text:       message,
		Timestamp:  time.Now().Unix(),
		Footer:     "AS API Service",
		FooterIcon: ":warning:",
	}

	if len(fields) > 0 {
		for key, value := range fields {
			field := SlackField{
				Title: key,
				Value: fmt.Sprintf("%v", value),
				Short: true,
			}
			attachment.Fields = append(attachment.Fields, field)
		}
	}

	slackMsg := SlackMessage{
		Channel:     s.config.Channel,
		Attachments: []SlackAttachment{attachment},
	}

	return s.SendMessage(ctx, slackMsg)
}

func (s *slackService) SendMessage(ctx context.Context, msg SlackMessage) error {
	if s.config.WebhookURL == "" {
		return nil
	}

	jsonData, err := json.Marshal(msg)
	if err != nil {
		return errors.Wrap(err, "failed to marshal Slack message")
	}

	req, err := http.NewRequestWithContext(ctx, "POST", s.config.WebhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return errors.Wrap(err, "failed to create HTTP request")
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(req)
	if err != nil {
		return errors.Wrap(err, "failed to send Slack message")
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return errors.Errorf("Slack API returned status code: %d", resp.StatusCode)
	}

	return nil
}

// Code generated by mockery v2.43.0. DO NOT EDIT.

package domain

import (
	dtos "as-api/as/dtos/user"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// PaymentService is an autogenerated mock type for the PaymentService type
type PaymentService struct {
	mock.Mock
}

// CreateVaultPaymentToken provides a mock function with given fields: ctx, req
func (_m *PaymentService) CreateVaultPaymentToken(ctx context.Context, req *dtos.CreateVaultPaymentTokenRequest) (*interface{}, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateVaultPaymentToken")
	}

	var r0 *interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *dtos.CreateVaultPaymentTokenRequest) (*interface{}, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *dtos.CreateVaultPaymentTokenRequest) *interface{}); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *dtos.CreateVaultPaymentTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateVaultSetupToken provides a mock function with given fields: ctx, req
func (_m *PaymentService) CreateVaultSetupToken(ctx context.Context, req *dtos.CreateVaultSetupTokenRequest) (*interface{}, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateVaultSetupToken")
	}

	var r0 *interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *dtos.CreateVaultSetupTokenRequest) (*interface{}, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *dtos.CreateVaultSetupTokenRequest) *interface{}); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *dtos.CreateVaultSetupTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetVaultTokens provides a mock function with given fields: ctx
func (_m *PaymentService) GetVaultTokens(ctx context.Context) (*dtos.GetVaultTokensResponse, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetVaultTokens")
	}

	var r0 *dtos.GetVaultTokensResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (*dtos.GetVaultTokensResponse, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) *dtos.GetVaultTokensResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dtos.GetVaultTokensResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// VerifyCreditCard provides a mock function with given fields: ctx, req
func (_m *PaymentService) VerifyCreditCard(ctx context.Context, req *dtos.VerifyCreditCardRequest) (*dtos.VerifyCreditCardResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for VerifyCreditCard")
	}

	var r0 *dtos.VerifyCreditCardResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *dtos.VerifyCreditCardRequest) (*dtos.VerifyCreditCardResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *dtos.VerifyCreditCardRequest) *dtos.VerifyCreditCardResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dtos.VerifyCreditCardResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *dtos.VerifyCreditCardRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewPaymentService creates a new instance of PaymentService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPaymentService(t interface {
	mock.TestingT
	Cleanup(func())
}) *PaymentService {
	mock := &PaymentService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

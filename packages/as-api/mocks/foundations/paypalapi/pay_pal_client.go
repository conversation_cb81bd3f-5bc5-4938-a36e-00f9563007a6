// Code generated by mockery v2.43.0. DO NOT EDIT.

package paypalapi

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"
)

// PayPalClient is an autogenerated mock type for the PayPalClient type
type PayPalClient struct {
	mock.Mock
}

// CreateVaultPaymentToken provides a mock function with given fields: ctx, req
func (_m *PayPalClient) CreateVaultPaymentToken(ctx context.Context, req paymenttokens.PaymentTokenRequest) (*paymenttokens.PaymentTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateVaultPaymentToken")
	}

	var r0 *paymenttokens.PaymentTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, paymenttokens.PaymentTokenRequest) (*paymenttokens.PaymentTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, paymenttokens.PaymentTokenRequest) *paymenttokens.PaymentTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*paymenttokens.PaymentTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, paymenttokens.PaymentTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteVaultPaymentToken provides a mock function with given fields: ctx, id
func (_m *PayPalClient) DeleteVaultPaymentToken(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteVaultPaymentToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetupTokensCreate provides a mock function with given fields: ctx, req
func (_m *PayPalClient) SetupTokensCreate(ctx context.Context, req paymenttokens.SetupTokenRequest) (*paymenttokens.SetupTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for SetupTokensCreate")
	}

	var r0 *paymenttokens.SetupTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, paymenttokens.SetupTokenRequest) (*paymenttokens.SetupTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, paymenttokens.SetupTokenRequest) *paymenttokens.SetupTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*paymenttokens.SetupTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, paymenttokens.SetupTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewPayPalClient creates a new instance of PayPalClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPayPalClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *PayPalClient {
	mock := &PayPalClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

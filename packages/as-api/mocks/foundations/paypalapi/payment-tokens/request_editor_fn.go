// Code generated by mockery v2.43.0. DO NOT EDIT.

package paymenttokens

import (
	context "context"
	http "net/http"

	mock "github.com/stretchr/testify/mock"
)

// RequestEditorFn is an autogenerated mock type for the RequestEditorFn type
type RequestEditorFn struct {
	mock.Mock
}

// Execute provides a mock function with given fields: ctx, req
func (_m *RequestEditorFn) Execute(ctx context.Context, req *http.Request) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for Execute")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *http.Request) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewRequestEditorFn creates a new instance of RequestEditorFn. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRequestEditorFn(t interface {
	mock.TestingT
	Cleanup(func())
}) *RequestEditorFn {
	mock := &RequestEditorFn{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

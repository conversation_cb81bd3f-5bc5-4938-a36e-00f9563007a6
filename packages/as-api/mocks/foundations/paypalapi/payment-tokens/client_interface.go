// Code generated by mockery v2.43.0. DO NOT EDIT.

package paymenttokens

import (
	context "context"
	http "net/http"

	io "io"

	mock "github.com/stretchr/testify/mock"

	payment_tokens "as-api/as/foundations/paypalapi/payment-tokens"
)

// ClientInterface is an autogenerated mock type for the ClientInterface type
type ClientInterface struct {
	mock.Mock
}

// CustomerPaymentTokensGet provides a mock function with given fields: ctx, params, reqEditors
func (_m *ClientInterface) CustomerPaymentTokensGet(ctx context.Context, params *payment_tokens.CustomerPaymentTokensGetParams, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CustomerPaymentTokensGet")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.CustomerPaymentTokensGetParams, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, params, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.CustomerPaymentTokensGetParams, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, params, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.CustomerPaymentTokensGetParams, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensCreate provides a mock function with given fields: ctx, params, body, reqEditors
func (_m *ClientInterface) PaymentTokensCreate(ctx context.Context, params *payment_tokens.PaymentTokensCreateParams, body payment_tokens.PaymentTokenRequest, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensCreate")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, payment_tokens.PaymentTokenRequest, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, params, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, payment_tokens.PaymentTokenRequest, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, params, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, payment_tokens.PaymentTokenRequest, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensCreateWithBody provides a mock function with given fields: ctx, params, contentType, body, reqEditors
func (_m *ClientInterface) PaymentTokensCreateWithBody(ctx context.Context, params *payment_tokens.PaymentTokensCreateParams, contentType string, body io.Reader, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, contentType, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensCreateWithBody")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, params, contentType, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensDelete provides a mock function with given fields: ctx, id, reqEditors
func (_m *ClientInterface) PaymentTokensDelete(ctx context.Context, id string, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensDelete")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, id, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, id, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, id, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensGet provides a mock function with given fields: ctx, id, reqEditors
func (_m *ClientInterface) PaymentTokensGet(ctx context.Context, id string, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensGet")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, id, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, id, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, id, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetupTokensCreate provides a mock function with given fields: ctx, params, body, reqEditors
func (_m *ClientInterface) SetupTokensCreate(ctx context.Context, params *payment_tokens.SetupTokensCreateParams, body payment_tokens.SetupTokenRequest, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SetupTokensCreate")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, payment_tokens.SetupTokenRequest, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, params, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, payment_tokens.SetupTokenRequest, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, params, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.SetupTokensCreateParams, payment_tokens.SetupTokenRequest, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetupTokensCreateWithBody provides a mock function with given fields: ctx, params, contentType, body, reqEditors
func (_m *ClientInterface) SetupTokensCreateWithBody(ctx context.Context, params *payment_tokens.SetupTokensCreateParams, contentType string, body io.Reader, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, contentType, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SetupTokensCreateWithBody")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, params, contentType, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.SetupTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetupTokensGet provides a mock function with given fields: ctx, id, reqEditors
func (_m *ClientInterface) SetupTokensGet(ctx context.Context, id string, reqEditors ...payment_tokens.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SetupTokensGet")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, id, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, id, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, id, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewClientInterface creates a new instance of ClientInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClientInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *ClientInterface {
	mock := &ClientInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

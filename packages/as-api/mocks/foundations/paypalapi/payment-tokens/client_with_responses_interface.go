// Code generated by mockery v2.43.0. DO NOT EDIT.

package paymenttokens

import (
	context "context"
	io "io"

	mock "github.com/stretchr/testify/mock"

	payment_tokens "as-api/as/foundations/paypalapi/payment-tokens"
)

// ClientWithResponsesInterface is an autogenerated mock type for the ClientWithResponsesInterface type
type ClientWithResponsesInterface struct {
	mock.Mock
}

// CustomerPaymentTokensGetWithResponse provides a mock function with given fields: ctx, params, reqEditors
func (_m *ClientWithResponsesInterface) CustomerPaymentTokensGetWithResponse(ctx context.Context, params *payment_tokens.CustomerPaymentTokensGetParams, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.CustomerPaymentTokensGetResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CustomerPaymentTokensGetWithResponse")
	}

	var r0 *payment_tokens.CustomerPaymentTokensGetResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.CustomerPaymentTokensGetParams, ...payment_tokens.RequestEditorFn) (*payment_tokens.CustomerPaymentTokensGetResp, error)); ok {
		return rf(ctx, params, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.CustomerPaymentTokensGetParams, ...payment_tokens.RequestEditorFn) *payment_tokens.CustomerPaymentTokensGetResp); ok {
		r0 = rf(ctx, params, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.CustomerPaymentTokensGetResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.CustomerPaymentTokensGetParams, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensCreateWithBodyWithResponse provides a mock function with given fields: ctx, params, contentType, body, reqEditors
func (_m *ClientWithResponsesInterface) PaymentTokensCreateWithBodyWithResponse(ctx context.Context, params *payment_tokens.PaymentTokensCreateParams, contentType string, body io.Reader, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensCreateResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, contentType, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensCreateWithBodyWithResponse")
	}

	var r0 *payment_tokens.PaymentTokensCreateResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensCreateResp, error)); ok {
		return rf(ctx, params, contentType, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) *payment_tokens.PaymentTokensCreateResp); ok {
		r0 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.PaymentTokensCreateResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensCreateWithResponse provides a mock function with given fields: ctx, params, body, reqEditors
func (_m *ClientWithResponsesInterface) PaymentTokensCreateWithResponse(ctx context.Context, params *payment_tokens.PaymentTokensCreateParams, body payment_tokens.PaymentTokenRequest, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensCreateResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensCreateWithResponse")
	}

	var r0 *payment_tokens.PaymentTokensCreateResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, payment_tokens.PaymentTokenRequest, ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensCreateResp, error)); ok {
		return rf(ctx, params, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, payment_tokens.PaymentTokenRequest, ...payment_tokens.RequestEditorFn) *payment_tokens.PaymentTokensCreateResp); ok {
		r0 = rf(ctx, params, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.PaymentTokensCreateResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.PaymentTokensCreateParams, payment_tokens.PaymentTokenRequest, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensDeleteWithResponse provides a mock function with given fields: ctx, id, reqEditors
func (_m *ClientWithResponsesInterface) PaymentTokensDeleteWithResponse(ctx context.Context, id string, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensDeleteResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensDeleteWithResponse")
	}

	var r0 *payment_tokens.PaymentTokensDeleteResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensDeleteResp, error)); ok {
		return rf(ctx, id, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) *payment_tokens.PaymentTokensDeleteResp); ok {
		r0 = rf(ctx, id, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.PaymentTokensDeleteResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, id, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PaymentTokensGetWithResponse provides a mock function with given fields: ctx, id, reqEditors
func (_m *ClientWithResponsesInterface) PaymentTokensGetWithResponse(ctx context.Context, id string, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensGetResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for PaymentTokensGetWithResponse")
	}

	var r0 *payment_tokens.PaymentTokensGetResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) (*payment_tokens.PaymentTokensGetResp, error)); ok {
		return rf(ctx, id, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) *payment_tokens.PaymentTokensGetResp); ok {
		r0 = rf(ctx, id, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.PaymentTokensGetResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, id, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetupTokensCreateWithBodyWithResponse provides a mock function with given fields: ctx, params, contentType, body, reqEditors
func (_m *ClientWithResponsesInterface) SetupTokensCreateWithBodyWithResponse(ctx context.Context, params *payment_tokens.SetupTokensCreateParams, contentType string, body io.Reader, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.SetupTokensCreateResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, contentType, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SetupTokensCreateWithBodyWithResponse")
	}

	var r0 *payment_tokens.SetupTokensCreateResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) (*payment_tokens.SetupTokensCreateResp, error)); ok {
		return rf(ctx, params, contentType, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) *payment_tokens.SetupTokensCreateResp); ok {
		r0 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.SetupTokensCreateResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.SetupTokensCreateParams, string, io.Reader, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetupTokensCreateWithResponse provides a mock function with given fields: ctx, params, body, reqEditors
func (_m *ClientWithResponsesInterface) SetupTokensCreateWithResponse(ctx context.Context, params *payment_tokens.SetupTokensCreateParams, body payment_tokens.SetupTokenRequest, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.SetupTokensCreateResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SetupTokensCreateWithResponse")
	}

	var r0 *payment_tokens.SetupTokensCreateResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, payment_tokens.SetupTokenRequest, ...payment_tokens.RequestEditorFn) (*payment_tokens.SetupTokensCreateResp, error)); ok {
		return rf(ctx, params, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *payment_tokens.SetupTokensCreateParams, payment_tokens.SetupTokenRequest, ...payment_tokens.RequestEditorFn) *payment_tokens.SetupTokensCreateResp); ok {
		r0 = rf(ctx, params, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.SetupTokensCreateResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *payment_tokens.SetupTokensCreateParams, payment_tokens.SetupTokenRequest, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetupTokensGetWithResponse provides a mock function with given fields: ctx, id, reqEditors
func (_m *ClientWithResponsesInterface) SetupTokensGetWithResponse(ctx context.Context, id string, reqEditors ...payment_tokens.RequestEditorFn) (*payment_tokens.SetupTokensGetResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for SetupTokensGetWithResponse")
	}

	var r0 *payment_tokens.SetupTokensGetResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) (*payment_tokens.SetupTokensGetResp, error)); ok {
		return rf(ctx, id, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, ...payment_tokens.RequestEditorFn) *payment_tokens.SetupTokensGetResp); ok {
		r0 = rf(ctx, id, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*payment_tokens.SetupTokensGetResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, ...payment_tokens.RequestEditorFn) error); ok {
		r1 = rf(ctx, id, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewClientWithResponsesInterface creates a new instance of ClientWithResponsesInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClientWithResponsesInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *ClientWithResponsesInterface {
	mock := &ClientWithResponsesInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

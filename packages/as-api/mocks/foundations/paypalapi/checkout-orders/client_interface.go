// Code generated by mockery v2.43.0. DO NOT EDIT.

package checkoutorders

import (
	checkout_orders "as-api/as/foundations/paypalapi/checkout-orders"
	context "context"

	http "net/http"

	io "io"

	mock "github.com/stretchr/testify/mock"
)

// ClientInterface is an autogenerated mock type for the ClientInterface type
type ClientInterface struct {
	mock.Mock
}

// CreateOrder provides a mock function with given fields: ctx, params, body, reqEditors
func (_m *ClientInterface) CreateOrder(ctx context.Context, params *checkout_orders.CreateOrderParams, body checkout_orders.OrderRequest, reqEditors ...checkout_orders.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrder")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, checkout_orders.OrderRequest, ...checkout_orders.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, params, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, checkout_orders.OrderRequest, ...checkout_orders.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, params, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *checkout_orders.CreateOrderParams, checkout_orders.OrderRequest, ...checkout_orders.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateOrderWithBody provides a mock function with given fields: ctx, params, contentType, body, reqEditors
func (_m *ClientInterface) CreateOrderWithBody(ctx context.Context, params *checkout_orders.CreateOrderParams, contentType string, body io.Reader, reqEditors ...checkout_orders.RequestEditorFn) (*http.Response, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, contentType, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrderWithBody")
	}

	var r0 *http.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, string, io.Reader, ...checkout_orders.RequestEditorFn) (*http.Response, error)); ok {
		return rf(ctx, params, contentType, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, string, io.Reader, ...checkout_orders.RequestEditorFn) *http.Response); ok {
		r0 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*http.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *checkout_orders.CreateOrderParams, string, io.Reader, ...checkout_orders.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewClientInterface creates a new instance of ClientInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClientInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *ClientInterface {
	mock := &ClientInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

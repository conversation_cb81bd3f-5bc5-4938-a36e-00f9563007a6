// Code generated by mockery v2.43.0. DO NOT EDIT.

package checkoutorders

import (
	checkout_orders "as-api/as/foundations/paypalapi/checkout-orders"
	context "context"

	io "io"

	mock "github.com/stretchr/testify/mock"
)

// ClientWithResponsesInterface is an autogenerated mock type for the ClientWithResponsesInterface type
type ClientWithResponsesInterface struct {
	mock.Mock
}

// CreateOrderWithBodyWithResponse provides a mock function with given fields: ctx, params, contentType, body, reqEditors
func (_m *ClientWithResponsesInterface) CreateOrderWithBodyWithResponse(ctx context.Context, params *checkout_orders.CreateOrderParams, contentType string, body io.Reader, reqEditors ...checkout_orders.RequestEditorFn) (*checkout_orders.CreateOrderResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, contentType, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrderWithBodyWithResponse")
	}

	var r0 *checkout_orders.CreateOrderResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, string, io.Reader, ...checkout_orders.RequestEditorFn) (*checkout_orders.CreateOrderResp, error)); ok {
		return rf(ctx, params, contentType, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, string, io.Reader, ...checkout_orders.RequestEditorFn) *checkout_orders.CreateOrderResp); ok {
		r0 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*checkout_orders.CreateOrderResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *checkout_orders.CreateOrderParams, string, io.Reader, ...checkout_orders.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, contentType, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateOrderWithResponse provides a mock function with given fields: ctx, params, body, reqEditors
func (_m *ClientWithResponsesInterface) CreateOrderWithResponse(ctx context.Context, params *checkout_orders.CreateOrderParams, body checkout_orders.OrderRequest, reqEditors ...checkout_orders.RequestEditorFn) (*checkout_orders.CreateOrderResp, error) {
	_va := make([]interface{}, len(reqEditors))
	for _i := range reqEditors {
		_va[_i] = reqEditors[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, params, body)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateOrderWithResponse")
	}

	var r0 *checkout_orders.CreateOrderResp
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, checkout_orders.OrderRequest, ...checkout_orders.RequestEditorFn) (*checkout_orders.CreateOrderResp, error)); ok {
		return rf(ctx, params, body, reqEditors...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *checkout_orders.CreateOrderParams, checkout_orders.OrderRequest, ...checkout_orders.RequestEditorFn) *checkout_orders.CreateOrderResp); ok {
		r0 = rf(ctx, params, body, reqEditors...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*checkout_orders.CreateOrderResp)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *checkout_orders.CreateOrderParams, checkout_orders.OrderRequest, ...checkout_orders.RequestEditorFn) error); ok {
		r1 = rf(ctx, params, body, reqEditors...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewClientWithResponsesInterface creates a new instance of ClientWithResponsesInterface. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClientWithResponsesInterface(t interface {
	mock.TestingT
	Cleanup(func())
}) *ClientWithResponsesInterface {
	mock := &ClientWithResponsesInterface{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// Code generated by mockery v2.43.0. DO NOT EDIT.

package repository

import (
	entities "as-api/as/foundations/db/entities"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// Repository is an autogenerated mock type for the Repository type
type Repository struct {
	mock.Mock
}

// CreateVaultToken provides a mock function with given fields: ctx, paypalUser
func (_m *Repository) CreateVaultToken(ctx context.Context, paypalUser *entities.PaypalUser) (*entities.PaypalUser, error) {
	ret := _m.Called(ctx, paypalUser)

	if len(ret) == 0 {
		panic("no return value specified for CreateVaultToken")
	}

	var r0 *entities.PaypalUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *entities.PaypalUser) (*entities.PaypalUser, error)); ok {
		return rf(ctx, paypalUser)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *entities.PaypalUser) *entities.PaypalUser); ok {
		r0 = rf(ctx, paypalUser)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*entities.PaypalUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *entities.PaypalUser) error); ok {
		r1 = rf(ctx, paypalUser)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DeleteVaultToken provides a mock function with given fields: ctx, id
func (_m *Repository) DeleteVaultToken(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteVaultToken")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindByCustomerID provides a mock function with given fields: ctx, customerID
func (_m *Repository) FindByCustomerID(ctx context.Context, customerID string) ([]*entities.PaypalUser, error) {
	ret := _m.Called(ctx, customerID)

	if len(ret) == 0 {
		panic("no return value specified for FindByCustomerID")
	}

	var r0 []*entities.PaypalUser
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]*entities.PaypalUser, error)); ok {
		return rf(ctx, customerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []*entities.PaypalUser); ok {
		r0 = rf(ctx, customerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*entities.PaypalUser)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, customerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewRepository creates a new instance of Repository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repository {
	mock := &Repository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

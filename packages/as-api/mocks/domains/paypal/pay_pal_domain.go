// Code generated by mockery v2.43.0. DO NOT EDIT.

package paypal

import (
	internalpaypal "as-api/as/internal/paypal"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// PayPalDomain is an autogenerated mock type for the PayPalDomain type
type PayPalDomain struct {
	mock.Mock
}

// CreateVaultPaymentToken provides a mock function with given fields: ctx, req
func (_m *PayPalDomain) CreateVaultPaymentToken(ctx context.Context, req *internalpaypal.CreateVaultPaymentTokenRequest) (*internalpaypal.CreateVaultPaymentTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateVaultPaymentToken")
	}

	var r0 *internalpaypal.CreateVaultPaymentTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internalpaypal.CreateVaultPaymentTokenRequest) (*internalpaypal.CreateVaultPaymentTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internalpaypal.CreateVaultPaymentTokenRequest) *internalpaypal.CreateVaultPaymentTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalpaypal.CreateVaultPaymentTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internalpaypal.CreateVaultPaymentTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateVaultSetupToken provides a mock function with given fields: ctx, req
func (_m *PayPalDomain) CreateVaultSetupToken(ctx context.Context, req *internalpaypal.CreateVaultSetupTokenRequest) (*internalpaypal.CreateVaultSetupTokenResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateVaultSetupToken")
	}

	var r0 *internalpaypal.CreateVaultSetupTokenResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internalpaypal.CreateVaultSetupTokenRequest) (*internalpaypal.CreateVaultSetupTokenResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internalpaypal.CreateVaultSetupTokenRequest) *internalpaypal.CreateVaultSetupTokenResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalpaypal.CreateVaultSetupTokenResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internalpaypal.CreateVaultSetupTokenRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetVaultTokenByCustomerID provides a mock function with given fields: ctx, customerID
func (_m *PayPalDomain) GetVaultTokenByCustomerID(ctx context.Context, customerID string) (*internalpaypal.VaultTokenData, error) {
	ret := _m.Called(ctx, customerID)

	if len(ret) == 0 {
		panic("no return value specified for GetVaultTokenByCustomerID")
	}

	var r0 *internalpaypal.VaultTokenData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internalpaypal.VaultTokenData, error)); ok {
		return rf(ctx, customerID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internalpaypal.VaultTokenData); ok {
		r0 = rf(ctx, customerID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalpaypal.VaultTokenData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, customerID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StoreVaultToken provides a mock function with given fields: ctx, customerID, paypalResp
func (_m *PayPalDomain) StoreVaultToken(ctx context.Context, customerID string, paypalResp *internalpaypal.CreateVaultPaymentTokenResponse) (*internalpaypal.VaultTokenData, error) {
	ret := _m.Called(ctx, customerID, paypalResp)

	if len(ret) == 0 {
		panic("no return value specified for StoreVaultToken")
	}

	var r0 *internalpaypal.VaultTokenData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internalpaypal.CreateVaultPaymentTokenResponse) (*internalpaypal.VaultTokenData, error)); ok {
		return rf(ctx, customerID, paypalResp)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *internalpaypal.CreateVaultPaymentTokenResponse) *internalpaypal.VaultTokenData); ok {
		r0 = rf(ctx, customerID, paypalResp)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalpaypal.VaultTokenData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *internalpaypal.CreateVaultPaymentTokenResponse) error); ok {
		r1 = rf(ctx, customerID, paypalResp)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewPayPalDomain creates a new instance of PayPalDomain. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewPayPalDomain(t interface {
	mock.TestingT
	Cleanup(func())
}) *PayPalDomain {
	mock := &PayPalDomain{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

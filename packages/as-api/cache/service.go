package cache

import (
	"context"
	"encoding/json"
	"time"

	"as-api/as/cache/repository"
	"as-api/as/foundations/db/entities"

	"github.com/pkg/errors"
)

type service struct {
	r repository.Repository
}

func NewCacheService(r repository.Repository) CacheService {
	return &service{
		r: r,
	}
}

func (s *service) Get(ctx context.Context, key string, dest interface{}) error {
	cacheItem, err := s.r.Get(ctx, key)
	if err != nil {
		return errors.Wrap(err, "get from repository")
	}

	if cacheItem == nil || cacheItem.Data == nil {
		return nil
	}

	dataBytes, err := json.Marshal(cacheItem.Data)
	if err != nil {
		return errors.Wrap(err, "marshal cache data")
	}

	if err := json.Unmarshal(dataBytes, dest); err != nil {
		return errors.Wrap(err, "unmarshal to destination")
	}

	return nil
}

func (s *service) Set(ctx context.Context, key string, value interface{}, ttl *time.Duration) error {
	dataBytes, err := json.Marshal(value)
	if err != nil {
		return errors.Wrap(err, "marshal value")
	}

	var jsonData entities.JSON
	if err := json.Unmarshal(dataBytes, &jsonData); err != nil {
		return errors.Wrap(err, "convert to JSON")
	}

	return s.r.Set(ctx, key, &jsonData)
}

func (s *service) Delete(ctx context.Context, key string) error {
	return s.r.Delete(ctx, key)
}

func (s *service) Exists(ctx context.Context, key string) (bool, error) {
	return s.r.Exists(ctx, key)
}

func (s *service) Clear(ctx context.Context) error {
	return s.r.Clear(ctx)
}

func (s *service) GetMultiple(ctx context.Context, keys []string) (map[string]*entities.JSON, error) {
	caches, err := s.r.GetMultiple(ctx, keys)
	if err != nil {
		return nil, errors.Wrap(err, "get multiple from repository")
	}

	result := make(map[string]*entities.JSON)
	for _, cache := range caches {
		result[cache.ID] = cache.Data
	}

	return result, nil
}

func (s *service) SetMultiple(ctx context.Context, items map[string]interface{}, ttl *time.Duration) error {
	var cacheItems []*entities.AppCach
	now := time.Now()

	for key, value := range items {
		dataBytes, err := json.Marshal(value)
		if err != nil {
			return errors.Wrapf(err, "marshal value for key %s", key)
		}

		var jsonData entities.JSON
		if err := json.Unmarshal(dataBytes, &jsonData); err != nil {
			return errors.Wrapf(err, "convert to JSON for key %s", key)
		}

		cacheItems = append(cacheItems, &entities.AppCach{
			ID:        key,
			Data:      &jsonData,
			UpdatedAt: &now,
		})
	}

	return s.r.SetMultiple(ctx, cacheItems)
}

func (s *service) DeleteMultiple(ctx context.Context, keys []string) error {
	return s.r.DeleteMultiple(ctx, keys)
}

func (s *service) CleanupExpired(ctx context.Context) error {
	return s.r.CleanupExpired(ctx)
}

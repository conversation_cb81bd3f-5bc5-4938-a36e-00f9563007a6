// Package dtos provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.1.0 DO NOT EDIT.
package dtos

import (
	"encoding/json"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for AgeGroupStatsAge.
const (
	N2029      AgeGroupStatsAge = "20-29"
	N3039      AgeGroupStatsAge = "30-39"
	N4049      AgeGroupStatsAge = "40-49"
	N5059      AgeGroupStatsAge = "50-59"
	N60AndOver AgeGroupStatsAge = "60-and-over"
	Under20    AgeGroupStatsAge = "under-20"
)

// Defines values for AuthRequestGrantType.
const (
	Password     AuthRequestGrantType = "password"
	RefreshToken AuthRequestGrantType = "refreshToken"
)

// Defines values for BankAccountAccountType.
const (
	Checking  BankAccountAccountType = "checking"
	Corporate BankAccountAccountType = "corporate"
	Savings   BankAccountAccountType = "savings"
)

// Defines values for BankAccountType.
const (
	IBAN         BankAccountType = "IBAN"
	JAPANACCOUNT BankAccountType = "JAPAN_ACCOUNT"
	SWIFTCODE    BankAccountType = "SWIFT_CODE"
	UAEACCOUNT   BankAccountType = "UAE_ACCOUNT"
)

// Defines values for BannerActionType.
const (
	InAppUrl BannerActionType = "inAppUrl"
	NoAction BannerActionType = "noAction"
	Query    BannerActionType = "query"
	Shop     BannerActionType = "shop"
	Url      BannerActionType = "url"
)

// Defines values for BannerStatus.
const (
	BannerStatusDraft     BannerStatus = "draft"
	BannerStatusPublished BannerStatus = "published"
)

// Defines values for CurrencyCode.
const (
	AED CurrencyCode = "AED"
	AUD CurrencyCode = "AUD"
	EUR CurrencyCode = "EUR"
	GBP CurrencyCode = "GBP"
	HKD CurrencyCode = "HKD"
	JPY CurrencyCode = "JPY"
	KRW CurrencyCode = "KRW"
	SAR CurrencyCode = "SAR"
	SGD CurrencyCode = "SGD"
	USD CurrencyCode = "USD"
)

// Defines values for InquiryCategory.
const (
	InquiryCategoryAreaService InquiryCategory = "areaService"
	InquiryCategoryGeneral     InquiryCategory = "general"
	InquiryCategoryPurchasing  InquiryCategory = "purchasing"
	InquiryCategorySelling     InquiryCategory = "selling"
)

// Defines values for InquiryStatus.
const (
	InquiryStatusClosed     InquiryStatus = "closed"
	InquiryStatusInProgress InquiryStatus = "inProgress"
	InquiryStatusNew        InquiryStatus = "new"
	InquiryStatusRejected   InquiryStatus = "rejected"
	InquiryStatusResolved   InquiryStatus = "resolved"
)

// Defines values for KycKycType.
const (
	Driverlicense KycKycType = "driverlicense"
	Passport      KycKycType = "passport"
)

// Defines values for NotificationSystemType.
const (
	NotificationSystemTypeNotice       NotificationSystemType = "notice"
	NotificationSystemTypeNotification NotificationSystemType = "notification"
)

// Defines values for NotificationType.
const (
	CAMPAIGNPROMOTION   NotificationType = "CAMPAIGN_PROMOTION"
	CARTPURCHASED       NotificationType = "CART_PURCHASED"
	FAVONSALE           NotificationType = "FAV_ON_SALE"
	FEATUREUPDATE       NotificationType = "FEATURE_UPDATE"
	IMPORTANTSYSTEM     NotificationType = "IMPORTANT_SYSTEM"
	INCOMINGMESSAGE     NotificationType = "INCOMING_MESSAGE"
	ITEMONSALE          NotificationType = "ITEM_ON_SALE"
	ITEMREMOVED         NotificationType = "ITEM_REMOVED"
	LOGINDETECTED       NotificationType = "LOGIN_DETECTED"
	PRICECHANGED        NotificationType = "PRICE_CHANGED"
	PRODUCTFAVORITED    NotificationType = "PRODUCT_FAVORITED"
	PRODUCTPURCHASED    NotificationType = "PRODUCT_PURCHASED"
	PRODUCTRECEIVED     NotificationType = "PRODUCT_RECEIVED"
	REMINDERUNRATEDTXN  NotificationType = "REMINDER_UNRATED_TXN"
	SELLERIMPORTANT     NotificationType = "SELLER_IMPORTANT"
	SHOPFAVORITED       NotificationType = "SHOP_FAVORITED"
	TXNPREPARING        NotificationType = "TXN_PREPARING"
	TXNSHIPPED          NotificationType = "TXN_SHIPPED"
	WITHDRAWALACCEPTED  NotificationType = "WITHDRAWAL_ACCEPTED"
	WITHDRAWALCOMPLETED NotificationType = "WITHDRAWAL_COMPLETED"
)

// Defines values for PaymentTokenType.
const (
	SETUPTOKEN PaymentTokenType = "SETUP_TOKEN"
)

// Defines values for ProductCondition.
const (
	ProductConditionLevel1 ProductCondition = "level1"
	ProductConditionLevel2 ProductCondition = "level2"
	ProductConditionLevel3 ProductCondition = "level3"
	ProductConditionLevel4 ProductCondition = "level4"
	ProductConditionLevel5 ProductCondition = "level5"
	ProductConditionLevel6 ProductCondition = "level6"
)

// Defines values for ProductRequestCondition.
const (
	ProductRequestConditionLevel1 ProductRequestCondition = "level1"
	ProductRequestConditionLevel2 ProductRequestCondition = "level2"
	ProductRequestConditionLevel3 ProductRequestCondition = "level3"
	ProductRequestConditionLevel4 ProductRequestCondition = "level4"
	ProductRequestConditionLevel5 ProductRequestCondition = "level5"
	ProductRequestConditionLevel6 ProductRequestCondition = "level6"
)

// Defines values for ProductRequestSalesStatus.
const (
	ProductRequestSalesStatusAvailable ProductRequestSalesStatus = "available"
	ProductRequestSalesStatusDraft     ProductRequestSalesStatus = "draft"
)

// Defines values for ProductSalesStatus.
const (
	ProductSalesStatusAvailable ProductSalesStatus = "available"
	ProductSalesStatusDraft     ProductSalesStatus = "draft"
	ProductSalesStatusOnSale    ProductSalesStatus = "onSale"
	ProductSalesStatusSold      ProductSalesStatus = "sold"
	ProductSalesStatusSuspended ProductSalesStatus = "suspended"
)

// Defines values for ProductTarget.
const (
	Children ProductTarget = "children"
	Men      ProductTarget = "men"
	Unisex   ProductTarget = "unisex"
	Women    ProductTarget = "women"
)

// Defines values for ProductTransactionStatus.
const (
	ProductTransactionStatusCanceled             ProductTransactionStatus = "canceled"
	ProductTransactionStatusCompleted            ProductTransactionStatus = "completed"
	ProductTransactionStatusOnHold               ProductTransactionStatus = "onHold"
	ProductTransactionStatusPreparingForShipment ProductTransactionStatus = "preparingForShipment"
	ProductTransactionStatusReceived             ProductTransactionStatus = "received"
	ProductTransactionStatusShipped              ProductTransactionStatus = "shipped"
	ProductTransactionStatusUnderReview          ProductTransactionStatus = "underReview"
	ProductTransactionStatusUnshipped            ProductTransactionStatus = "unshipped"
)

// Defines values for ReportItemType.
const (
	ReportItemTypeProduct ReportItemType = "product"
	ReportItemTypeSeller  ReportItemType = "seller"
	ReportItemTypeUser    ReportItemType = "user"
)

// Defines values for ReportReporterType.
const (
	ReportReporterTypeSeller ReportReporterType = "seller"
	ReportReporterTypeUser   ReportReporterType = "user"
)

// Defines values for ReportStatus.
const (
	ReportStatusCompleted   ReportStatus = "completed"
	ReportStatusInProgress  ReportStatus = "inProgress"
	ReportStatusNotHandled  ReportStatus = "notHandled"
	ReportStatusPending     ReportStatus = "pending"
	ReportStatusUncompleted ReportStatus = "uncompleted"
)

// Defines values for SearchProductsRequestSort.
const (
	SearchProductsRequestSortFavorites SearchProductsRequestSort = "favorites"
	SearchProductsRequestSortNewest    SearchProductsRequestSort = "newest"
	SearchProductsRequestSortPriceHigh SearchProductsRequestSort = "priceHigh"
	SearchProductsRequestSortPriceLow  SearchProductsRequestSort = "priceLow"
	SearchProductsRequestSortViews     SearchProductsRequestSort = "views"
)

// Defines values for SellerAccountType.
const (
	Bussiness  SellerAccountType = "bussiness"
	Individual SellerAccountType = "individual"
)

// Defines values for SignupRequestRegisterType.
const (
	SignupRequestRegisterTypeBusiness SignupRequestRegisterType = "business"
	SignupRequestRegisterTypeGeneral  SignupRequestRegisterType = "general"
	SignupRequestRegisterTypeSeller   SignupRequestRegisterType = "seller"
)

// Defines values for SizeTable.
const (
	General SizeTable = "general"
	Hat     SizeTable = "hat"
	Pants   SizeTable = "pants"
	Ring    SizeTable = "ring"
	Shoes   SizeTable = "shoes"
)

// Defines values for TaxName.
const (
	CONSUMPTIONTAX TaxName = "CONSUMPTION_TAX"
	GST            TaxName = "GST"
	SALESTAX       TaxName = "SALES_TAX"
	VAT            TaxName = "VAT"
)

// Defines values for UpdatePublishSellerRequestPublicStatus.
const (
	UpdatePublishSellerRequestPublicStatusDraft  UpdatePublishSellerRequestPublicStatus = "draft"
	UpdatePublishSellerRequestPublicStatusPublic UpdatePublishSellerRequestPublicStatus = "public"
)

// Defines values for UpsertFCMTokenRequestDeviceType.
const (
	Android UpsertFCMTokenRequestDeviceType = "android"
	Ios     UpsertFCMTokenRequestDeviceType = "ios"
	Web     UpsertFCMTokenRequestDeviceType = "web"
)

// Defines values for GetCountriesParamsMode.
const (
	GetCountriesParamsModeInquiry  GetCountriesParamsMode = "inquiry"
	GetCountriesParamsModeRegister GetCountriesParamsMode = "register"
)

// Defines values for GetFollowedSellerProductsParamsSort.
const (
	GetFollowedSellerProductsParamsSortFavorites GetFollowedSellerProductsParamsSort = "favorites"
	GetFollowedSellerProductsParamsSortNewest    GetFollowedSellerProductsParamsSort = "newest"
	GetFollowedSellerProductsParamsSortPriceHigh GetFollowedSellerProductsParamsSort = "priceHigh"
	GetFollowedSellerProductsParamsSortPriceLow  GetFollowedSellerProductsParamsSort = "priceLow"
	GetFollowedSellerProductsParamsSortViews     GetFollowedSellerProductsParamsSort = "views"
)

// Defines values for GetSoldItemsParamsOrderBy.
const (
	GetSoldItemsParamsOrderByAsc  GetSoldItemsParamsOrderBy = "asc"
	GetSoldItemsParamsOrderByDesc GetSoldItemsParamsOrderBy = "desc"
)

// Defines values for GetSellerProductsParamsSort.
const (
	Favorites GetSellerProductsParamsSort = "favorites"
	Newest    GetSellerProductsParamsSort = "newest"
	PriceHigh GetSellerProductsParamsSort = "priceHigh"
	PriceLow  GetSellerProductsParamsSort = "priceLow"
	Views     GetSellerProductsParamsSort = "views"
)

// Defines values for GetTermsParamsType.
const (
	BuyerProtection          GetTermsParamsType = "buyerProtection"
	CommercialTransactionLaw GetTermsParamsType = "commercialTransactionLaw"
	CommunityGuidelines      GetTermsParamsType = "communityGuidelines"
	PaymentTerms             GetTermsParamsType = "paymentTerms"
	PrivacyPolicy            GetTermsParamsType = "privacyPolicy"
	TermsOfUseForGeneral     GetTermsParamsType = "termsOfUseForGeneral"
	TermsOfUseForSeller      GetTermsParamsType = "termsOfUseForSeller"
)

// Defines values for GetUserThreadsParamsStatus.
const (
	Unread    GetUserThreadsParamsStatus = "unread"
	Unreplied GetUserThreadsParamsStatus = "unreplied"
)

// Defines values for GetPurchasedItemsParamsTransactionStatus.
const (
	GetPurchasedItemsParamsTransactionStatusAll       GetPurchasedItemsParamsTransactionStatus = "all"
	GetPurchasedItemsParamsTransactionStatusCompleted GetPurchasedItemsParamsTransactionStatus = "completed"
	GetPurchasedItemsParamsTransactionStatusShipped   GetPurchasedItemsParamsTransactionStatus = "shipped"
	GetPurchasedItemsParamsTransactionStatusUnshipped GetPurchasedItemsParamsTransactionStatus = "unshipped"
)

// Defines values for GetPurchasedItemsParamsOrderBy.
const (
	GetPurchasedItemsParamsOrderByAsc  GetPurchasedItemsParamsOrderBy = "asc"
	GetPurchasedItemsParamsOrderByDesc GetPurchasedItemsParamsOrderBy = "desc"
)

// Defines values for GetUserNotificationsParamsSystemType.
const (
	GetUserNotificationsParamsSystemTypeNotice       GetUserNotificationsParamsSystemType = "notice"
	GetUserNotificationsParamsSystemTypeNotification GetUserNotificationsParamsSystemType = "notification"
)

// AddFavoriteProductRequest Request body for adding a favorite product
type AddFavoriteProductRequest struct {
	// ProductId Product ID to add to favorites
	ProductId string `json:"productId"`
}

// AddFavoriteSellersRequest Request body for adding a favorite seller
type AddFavoriteSellersRequest struct {
	// SellerId Seller ID to add to favorites
	SellerId string `json:"sellerId"`
}

// Address defines model for Address.
type Address struct {
	// Address1 Address line 1
	Address1 *string `json:"address1,omitempty"`

	// Address2 Address line 2
	Address2 *string `json:"address2,omitempty"`

	// City City of the address
	City *string `json:"city,omitempty"`

	// Country Country of the address
	Country *string `json:"country,omitempty"`

	// FirstName First name of the user
	FirstName *string `json:"firstName,omitempty"`

	// FullName Full name of the user
	FullName *string `json:"fullName,omitempty"`

	// IsDifferentFromResidence Whether the address is different from the residence address
	IsDifferentFromResidence *bool `json:"isDifferentFromResidence,omitempty"`

	// LastName Last name of the user
	LastName *string `json:"lastName,omitempty"`
	Mobile   *Phone  `json:"mobile,omitempty"`
	Phone    *Phone  `json:"phone,omitempty"`

	// PostalCode Postal code of the address
	PostalCode *string `json:"postalCode,omitempty"`

	// RegionId Region id of the address
	RegionId *string `json:"regionId,omitempty"`

	// State State of the address
	State *string `json:"state,omitempty"`
}

// Admin defines model for Admin.
type Admin struct {
	Id   *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
}

// AgeGroupStats Statistics for a specific age group
type AgeGroupStats struct {
	// Age Age group range
	Age AgeGroupStatsAge `json:"age"`

	// GenderBreakdown Gender breakdown statistics
	GenderBreakdown GenderBreakdown `json:"genderBreakdown"`

	// Total Total number of followers in this age group
	Total int `json:"total"`
}

// AgeGroupStatsAge Age group range
type AgeGroupStatsAge string

// Attachment defines model for Attachment.
type Attachment struct {
	FileName *string `json:"fileName,omitempty"`
	FilePath string  `json:"filePath"`
	FileSize *int    `json:"fileSize,omitempty"`
	FileType *string `json:"fileType,omitempty"`
}

// AuthRequest Auth Request
type AuthRequest struct {
	GrantType    AuthRequestGrantType `json:"grantType" validate:"oneof=password refreshToken"`
	Password     *string              `json:"password,omitempty" validate:"omitempty,gt=0"`
	RefreshToken *string              `json:"refreshToken,omitempty" validate:"omitempty,gt=0"`
	Username     *string              `json:"username,omitempty" validate:"omitempty,gt=0"`
}

// AuthRequestGrantType defines model for AuthRequest.GrantType.
type AuthRequestGrantType string

// AuthResponse Auth Response
type AuthResponse struct {
	// AccessToken Access token
	AccessToken string `json:"accessToken"`

	// RefreshToken Refresh token
	RefreshToken string `json:"refreshToken"`
}

// BankAccount Validation model for bank account profile information
type BankAccount struct {
	// AccountHolderKatakana Account holder name in katakana validation (JP only)
	AccountHolderKatakana *string `json:"accountHolderKatakana,omitempty"`

	// AccountHolderName Account holder name validation
	AccountHolderName string `json:"accountHolderName"`

	// AccountNumber Account number format validation
	AccountNumber string `json:"accountNumber"`

	// AccountType Validated account types
	AccountType BankAccountAccountType `json:"accountType"`

	// BankCode Bank code format validation
	BankCode *string `json:"bankCode,omitempty"`

	// BankName Bank name with length constraints
	BankName string `json:"bankName"`

	// BranchCode Branch code format validation
	BranchCode *string `json:"branchCode,omitempty"`
	BranchName *string `json:"branchName,omitempty"`

	// Country Country/Region
	Country *string `json:"country,omitempty"`

	// Iban IBAN code
	Iban *string `json:"iban,omitempty"`

	// Pin PIN for bank account
	Pin *string `json:"pin,omitempty"`

	// RoutingNumber Routing number
	RoutingNumber *string `json:"routingNumber,omitempty"`

	// SwiftCode SWIFT/BIC code format validation
	SwiftCode *string `json:"swiftCode,omitempty"`
}

// BankAccountAccountType Validated account types
type BankAccountAccountType string

// BankAccountType defines model for BankAccountType.
type BankAccountType string

// Banner defines model for Banner.
type Banner struct {
	Action             *BannerAction                 `json:"action,omitempty"`
	BannerTranslations *map[string]BannerTranslation `json:"bannerTranslations,omitempty"`
	CountryCodes       *[]string                     `json:"countryCodes,omitempty"`
	Id                 *string                       `json:"id,omitempty"`
	Products           *[]Product                    `json:"products,omitempty"`
}

// BannerAction defines model for BannerAction.
type BannerAction struct {
	// InAppUrl In-app URL to redirect to
	InAppUrl *string      `json:"inAppUrl,omitempty"`
	Query    *BannerQuery `json:"query,omitempty"`

	// Shop Shop ID to redirect to
	Shop *string `json:"shop,omitempty"`

	// Type Type of banner action
	Type *BannerActionType `json:"type,omitempty"`

	// Url URL to redirect to
	Url *string `json:"url,omitempty"`
}

// BannerActionType Type of banner action
type BannerActionType string

// BannerProducts defines model for BannerProducts.
type BannerProducts struct {
	Banner   *Banner    `json:"banner,omitempty"`
	Products *[]Product `json:"products,omitempty"`
}

// BannerQuery defines model for BannerQuery.
type BannerQuery struct {
	BrandNames   *[]string `json:"brandNames,omitempty"`
	CategoryIds  *[]string `json:"categoryIds,omitempty"`
	Conditions   *[]string `json:"conditions,omitempty"`
	CountryCodes *[]string `json:"countryCodes,omitempty"`

	// Keyword Keyword to search for
	Keyword *string `json:"keyword,omitempty"`

	// MaxPrice Maximum price to search for
	MaxPrice *float64 `json:"maxPrice,omitempty"`

	// MinPrice Minimum price to search for
	MinPrice     *float64              `json:"minPrice,omitempty"`
	SaleStatuses *[]ProductSalesStatus `json:"saleStatuses,omitempty"`
	Sizes        *[]string             `json:"sizes,omitempty"`
	Targets      *[]ProductTarget      `json:"targets,omitempty"`
}

// BannerStatus defines model for BannerStatus.
type BannerStatus string

// BannerTranslation additionalProperty is language code
type BannerTranslation struct {
	BannerImageUrl *string       `json:"bannerImageUrl,omitempty"`
	HeroImageUrl   *string       `json:"heroImageUrl,omitempty"`
	Position       *[]string     `json:"position,omitempty"`
	Status         *BannerStatus `json:"status,omitempty"`
	Title          *string       `json:"title,omitempty"`
}

// BlockUser defines model for BlockUser.
type BlockUser struct {
	Id       *string `json:"id,omitempty"`
	SellerId *string `json:"sellerId,omitempty"`
	UserId   *string `json:"userId,omitempty"`
}

// BlockUserRequest Block User Request
type BlockUserRequest struct {
	// UserId Name of the product
	UserId string `json:"userId"`
}

// BlockUsersResponse Get block users response object
type BlockUsersResponse struct {
	Data *[]BlockUser `json:"data,omitempty"`
}

// CancelAccountRequest Request body for canceling an account
type CancelAccountRequest struct {
	// AppDifficultToUse Whether the app is difficult to use
	AppDifficultToUse *bool `json:"appDifficultToUse,omitempty"`

	// DeliveryTooLong Whether the delivery is too long
	DeliveryTooLong *bool `json:"deliveryTooLong,omitempty"`

	// DontKnowHowToList Whether the user does not know how to list
	DontKnowHowToList *bool `json:"dontKnowHowToList,omitempty"`

	// ItemProblem Whether the problem is with the item
	ItemProblem *bool `json:"itemProblem,omitempty"`

	// ItemsDidNotSell Whether the items did not sell
	ItemsDidNotSell *bool `json:"itemsDidNotSell,omitempty"`

	// OtherReason Other reason for canceling the account
	OtherReason *string `json:"otherReason,omitempty"`

	// PricesTooHigh Whether the price is too high
	PricesTooHigh *bool `json:"pricesTooHigh,omitempty"`

	// ProductsUnavailable Whether the product or brand is unavailable
	ProductsUnavailable *bool `json:"productsUnavailable,omitempty"`

	// SellerUntrustworthy Whether the seller is untrustworthy
	SellerUntrustworthy *bool `json:"sellerUntrustworthy,omitempty"`

	// SituationChanged Whether the situation has situation changed
	SituationChanged *bool `json:"situationChanged,omitempty"`

	// TooManyEmails Whether the user receives too many emails
	TooManyEmails *bool `json:"tooManyEmails,omitempty"`
}

// CancelSubscriptionAndroidRequest defines model for CancelSubscriptionAndroidRequest.
type CancelSubscriptionAndroidRequest struct {
	Message struct {
		Data *string `json:"data,omitempty"`
	} `json:"message"`
	SignedPayload *string `json:"signedPayload,omitempty"`
}

// CancelSubscriptionIOSRequest defines model for CancelSubscriptionIOSRequest.
type CancelSubscriptionIOSRequest struct {
	SignedPayload *string `json:"signedPayload,omitempty"`
}

// CardRequest A Resource representing a request to vault a Card
type CardRequest struct {
	BillingAddress *Address `json:"billing_address,omitempty"`
}

// Cart Response schema for cart
type Cart struct {
	AdministrativeFee          *float64 `json:"administrativeFee,omitempty"`
	AdministrativeFeeRate      *float64 `json:"administrativeFeeRate,omitempty"`
	AdministrativeFeeTaxAmount *float64 `json:"administrativeFeeTaxAmount,omitempty"`
	AdministrativeFeeTaxRate   *float64 `json:"administrativeFeeTaxRate,omitempty"`
	Amount                     *float64 `json:"amount,omitempty"`
	DiscountAmount             *float64 `json:"discountAmount,omitempty"`

	// Items List of items in cart
	Items                []CartItem  `json:"items"`
	PaymentMethod        *CreditCard `json:"paymentMethod,omitempty"`
	PurchaseFee          *float64    `json:"purchaseFee,omitempty"`
	PurchaseFeeTaxAmount *float64    `json:"purchaseFeeTaxAmount,omitempty"`
	PurchaseFeeTaxRate   *float64    `json:"purchaseFeeTaxRate,omitempty"`
	ShippingAddress      *Address    `json:"shippingAddress,omitempty"`
	TaxAmount            *float64    `json:"taxAmount,omitempty"`

	// TaxInfo Tax information for an order
	TaxInfo     *TaxInfo `json:"taxInfo,omitempty"`
	TaxRate     *float64 `json:"taxRate,omitempty"`
	TotalAmount *float64 `json:"totalAmount,omitempty"`
}

// CartItem Model for cart item information
type CartItem struct {
	// Amount Amount (excluding tax)
	Amount *float64 `json:"amount,omitempty"`

	// DiscountAmount Discount amount applied to this item
	DiscountAmount *float64 `json:"discountAmount,omitempty"`

	// Id Unique identifier of the cart item
	Id string `json:"id"`

	// IsFavoriteSeller Whether the product is of a favorite seller
	IsFavoriteSeller *bool `json:"isFavoriteSeller,omitempty"`

	// Note Note for seller
	Note    *string  `json:"note,omitempty"`
	Product *Product `json:"product,omitempty"`

	// ProductId ID of the product in cart
	ProductId string `json:"productId"`

	// TaxAmount Tax amount
	TaxAmount *float64       `json:"taxAmount,omitempty"`
	TaxInfos  *[]TaxItemInfo `json:"taxInfos,omitempty"`

	// TaxRate Tax rate
	TaxRate *float64 `json:"taxRate,omitempty"`

	// TotalAmount Total amount
	TotalAmount *float64 `json:"totalAmount,omitempty"`

	// UserId Unique identifier of the user
	UserId string `json:"userId"`
}

// CartItemsResponse Response schema for cart
type CartItemsResponse struct {
	AdministrativeFee          *float64 `json:"administrativeFee,omitempty"`
	AdministrativeFeeRate      *float64 `json:"administrativeFeeRate,omitempty"`
	AdministrativeFeeTaxAmount *float64 `json:"administrativeFeeTaxAmount,omitempty"`
	AdministrativeFeeTaxRate   *float64 `json:"administrativeFeeTaxRate,omitempty"`
	Amount                     *float64 `json:"amount,omitempty"`
	DiscountAmount             *float64 `json:"discountAmount,omitempty"`

	// Items List of items in cart
	Items                []CartItem  `json:"items"`
	PaymentMethod        *CreditCard `json:"paymentMethod,omitempty"`
	PurchaseFee          *float64    `json:"purchaseFee,omitempty"`
	PurchaseFeeTaxAmount *float64    `json:"purchaseFeeTaxAmount,omitempty"`
	PurchaseFeeTaxRate   *float64    `json:"purchaseFeeTaxRate,omitempty"`
	ShippingAddress      *Address    `json:"shippingAddress,omitempty"`
	TaxAmount            *float64    `json:"taxAmount,omitempty"`

	// TaxInfo Tax information for an order
	TaxInfo     *TaxInfo `json:"taxInfo,omitempty"`
	TaxRate     *float64 `json:"taxRate,omitempty"`
	TotalAmount *float64 `json:"totalAmount,omitempty"`
}

// CheckPinRequest defines model for CheckPinRequest.
type CheckPinRequest struct {
	// Pin Admin PIN for additional security features
	Pin Pin `json:"pin"`
}

// CompanyInfo Validation model for indentity information
type CompanyInfo struct {
	// CompanyName Company name
	CompanyName *string `json:"companyName,omitempty"`

	// ContactName Contact person name
	ContactName *string `json:"contactName,omitempty"`

	// DunsNumber DUNS number
	DunsNumber *string `json:"dunsNumber,omitempty"`

	// Email Company/Contact person's email address
	Email *string `json:"email,omitempty"`

	// PhoneNumber Phone number with international format validation
	PhoneNumber *string `json:"phoneNumber,omitempty"`

	// WebsiteURL Website URL
	WebsiteURL *string `json:"websiteURL,omitempty"`
}

// CountUnreadNotificationsResponse defines model for CountUnreadNotificationsResponse.
type CountUnreadNotificationsResponse struct {
	// CountNotices The number of unread notices
	CountNotices int `json:"countNotices"`

	// CountNotifications The number of unread notifications
	CountNotifications int `json:"countNotifications"`
}

// CountUnreadThreadsResponse defines model for CountUnreadThreadsResponse.
type CountUnreadThreadsResponse struct {
	// CountUnreadThreads The number of unread threads
	CountUnreadThreads int `json:"countUnreadThreads"`
}

// CountriesResponse defines model for CountriesResponse.
type CountriesResponse struct {
	Items *[]Country `json:"items,omitempty"`
}

// Country defines model for Country.
type Country struct {
	BankAccountType *BankAccountType   `json:"bankAccountType,omitempty"`
	Code            *string            `json:"code,omitempty"`
	Currencies      *[]Currency        `json:"currencies,omitempty"`
	DisplayName     *map[string]string `json:"displayName,omitempty"`
	IsCorporateOnly *bool              `json:"isCorporateOnly,omitempty"`
	PhoneCode       *string            `json:"phoneCode,omitempty"`
	Regions         *[]Region          `json:"regions,omitempty"`
	TaxName         *TaxName           `json:"taxName,omitempty"`
}

// CountryFollowerStats Follower statistics for a specific country
type CountryFollowerStats struct {
	// AgeStats Age-based statistics for this country
	AgeStats []AgeGroupStats `json:"ageStats"`

	// CountryCode ISO 3166-1 alpha-3 country code
	CountryCode string `json:"countryCode"`

	// GenderBreakdown Gender breakdown statistics
	GenderBreakdown GenderBreakdown `json:"genderBreakdown"`

	// Total Total number of followers from this country
	Total int `json:"total"`
}

// CreateOrderRequest defines model for CreateOrderRequest.
type CreateOrderRequest struct {
	// IsAddFavoriteSeller Whether to add favorite seller
	IsAddFavoriteSeller *bool       `json:"isAddFavoriteSeller,omitempty"`
	Items               *[]CartItem `json:"items,omitempty"`
}

// CreateOrderResponse defines model for CreateOrderResponse.
type CreateOrderResponse struct {
	AccessId    *string `json:"accessId,omitempty"`
	Id          *string `json:"id,omitempty"`
	RedirectUrl *string `json:"redirectUrl,omitempty"`
	Status      *int    `json:"status,omitempty"`
}

// CreateReviewRequest Create Review Request
type CreateReviewRequest struct {
	PackRating   *float64 `json:"packRating,omitempty"`
	PoliteRating *float64 `json:"politeRating,omitempty"`
	Rating       *float64 `json:"rating,omitempty"`
	SpeedRating  *float64 `json:"speedRating,omitempty"`
}

// CreateThreadRequest defines model for CreateThreadRequest.
type CreateThreadRequest struct {
	// InitialMessage Initial message content for the thread
	InitialMessage *string `json:"initialMessage,omitempty"`

	// ProductId Optional product ID if the thread is related to a product
	ProductId *string `json:"productId,omitempty"`

	// SellerId ID of the seller to create thread with
	SellerId *string `json:"sellerId,omitempty"`

	// UserId ID of the user to create thread with
	UserId *string `json:"userId,omitempty"`
}

// CreateThreadResponse defines model for CreateThreadResponse.
type CreateThreadResponse = Thread

// CreateVaultPaymentTokenRequest defines model for CreateVaultPaymentTokenRequest.
type CreateVaultPaymentTokenRequest struct {
	VaultSetupToken string `json:"vaultSetupToken"`
}

// CreateVaultPaymentTokenResponse defines model for CreateVaultPaymentTokenResponse.
type CreateVaultPaymentTokenResponse = interface{}

// CreateVaultSetupTokenRequest defines model for CreateVaultSetupTokenRequest.
type CreateVaultSetupTokenRequest struct {
	// PaymentSource The payment method to vault with the instrument details
	PaymentSource PaymentSourceRequest `json:"payment_source"`
}

// CreateVaultSetupTokenResponse defines model for CreateVaultSetupTokenResponse.
type CreateVaultSetupTokenResponse = interface{}

// CreditCard defines model for CreditCard.
type CreditCard struct {
	Brand          *string `json:"brand,omitempty"`
	CardHolderName *string `json:"cardHolderName,omitempty"`
	CardNumber     *string `json:"cardNumber,omitempty"`
	ExpiryMonth    *string `json:"expiryMonth,omitempty"`
	ExpiryYear     *string `json:"expiryYear,omitempty"`
}

// Currency defines model for Currency.
type Currency struct {
	// Code Currency code
	Code *CurrencyCode `json:"code,omitempty"`

	// IsBankAccountCurrency Currency is bank account currency
	IsBankAccountCurrency *bool `json:"isBankAccountCurrency,omitempty"`
}

// CurrencyCode Currency code
type CurrencyCode string

// DailySalesSummaryResponse defines model for DailySalesSummaryResponse.
type DailySalesSummaryResponse struct {
	// Date Date of the summary in YYYY-MM-DD format
	Date openapi_types.Date `json:"date"`

	// OrderAmounts Order amount metrics
	OrderAmounts SalesSummaryOrderAmounts `json:"orderAmounts"`

	// OrderAmountsTaxIncluded Order amount metrics
	OrderAmountsTaxIncluded SalesSummaryOrderAmounts `json:"orderAmountsTaxIncluded"`

	// OrderCounts Order count metrics
	OrderCounts  SalesSummaryOrderCounts    `json:"orderCounts"`
	OrderDetails *[]SalesSummaryOrderDetail `json:"orderDetails,omitempty"`

	// SalesAmounts Order amount metrics
	SalesAmounts SalesSummaryOrderAmounts `json:"salesAmounts"`

	// SalesFee Total sales fee (platform commission)
	SalesFee float64 `json:"salesFee"`

	// Vat Total VAT amount
	Vat float64 `json:"vat"`
}

// DeleteFCMTokenRequest defines model for DeleteFCMTokenRequest.
type DeleteFCMTokenRequest struct {
	// FcmToken Firebase Cloud Messaging token to be removed
	FcmToken string `json:"fcmToken"`
}

// DeleteFavoriteProductsRequest Request body for deleting multiple favorite products
type DeleteFavoriteProductsRequest struct {
	// ProductIds List of product IDs to remove from favorites
	ProductIds []string `json:"productIds"`
}

// DeleteFavoriteSellersRequest Request body for deleting multiple favorite sellers
type DeleteFavoriteSellersRequest struct {
	// SellerIds List of seller IDs to remove from favorites
	SellerIds []string `json:"sellerIds"`
}

// EmailNotificationSettings defines model for EmailNotificationSettings.
type EmailNotificationSettings struct {
	// CampaignsAndPromotions Get updates on special offers, deals, and promotions.
	CampaignsAndPromotions *bool `json:"campaignsAndPromotions,omitempty"`

	// CartAndFavorites Get alerts on price changes and stock updates.
	CartAndFavorites *bool `json:"cartAndFavorites,omitempty"`

	// FavoritedShops Get notified when someone favorites your shop.
	FavoritedShops *bool `json:"favoritedShops,omitempty"`

	// ImportantNotices Get important updates about your account and services.
	ImportantNotices *bool `json:"importantNotices,omitempty"`

	// ListedItems Get updates when your listed items are purchased.
	ListedItems *bool `json:"listedItems,omitempty"`

	// Login Get alerts when a new device logs in.
	Login *bool `json:"login,omitempty"`

	// Messages Get notifications when you receive a new message.
	Messages *bool `json:"messages,omitempty"`

	// OrderStatus Get updates on shipping and purchase status.
	OrderStatus *bool `json:"orderStatus,omitempty"`

	// Withdrawals Get updates on your withdrawal request status.
	Withdrawals *bool `json:"withdrawals,omitempty"`
}

// ErrorResponse Response when abnormal
type ErrorResponse struct {
	// Code Error code
	Code *string `json:"code,omitempty"`

	// DebugMessage Debug message (for development)
	DebugMessage *string `json:"debug_message,omitempty"`

	// Message Error message
	Message *string `json:"message,omitempty"`

	// RequestId Request id where the error occurred
	RequestId *string `json:"request_id,omitempty"`
}

// Faq defines model for Faq.
type Faq struct {
	Content    *FaqContent        `json:"content,omitempty"`
	CreatedAt  *string            `json:"createdAt,omitempty"`
	Id         *string            `json:"id,omitempty"`
	IsCategory *bool              `json:"isCategory,omitempty"`
	Title      *map[string]string `json:"title,omitempty"`
	UpdatedAt  *string            `json:"updatedAt,omitempty"`
}

// FaqContent defines model for FaqContent.
type FaqContent struct {
	Attachments *map[string][]string `json:"attachments,omitempty"`
	BodyHtml    *map[string]string   `json:"bodyHtml,omitempty"`
}

// FaqsResponse defines model for FaqsResponse.
type FaqsResponse struct {
	Items *[]Faq `json:"items,omitempty"`
}

// FollowerTrends Follower statistics and trends
type FollowerTrends struct {
	// Countries List of countries with follower statistics
	Countries []CountryFollowerStats `json:"countries"`
}

// FollowingUser Model for following users
type FollowingUser struct {
	Id       *string `json:"id,omitempty"`
	SellerId *string `json:"sellerId,omitempty"`
	User     *User   `json:"user,omitempty"`
	UserId   *string `json:"userId,omitempty"`
}

// FollowingUserResponse Get following users response object
type FollowingUserResponse struct {
	Data *[]FollowingUser `json:"data,omitempty"`
}

// GenderBreakdown Gender breakdown statistics
type GenderBreakdown struct {
	// Female Number of female followers
	Female int `json:"female"`

	// Male Number of male followers
	Male int `json:"male"`

	// Other Number of followers with other/unspecified gender
	Other int `json:"other"`
}

// GetBannersResponse Get banners response object
type GetBannersResponse struct {
	Data *[]Banner `json:"data,omitempty"`
}

// GetBrandsResponse Response schema for retrieving a list of brands
type GetBrandsResponse struct {
	// Items List of available brands
	Items *[]ProductBrand `json:"items,omitempty"`
}

// GetFavoriteProductsResponse Response schema for favorite products list
type GetFavoriteProductsResponse struct {
	// Items List of favorite products
	Items *[]Product `json:"items,omitempty"`
}

// GetFavoriteSellersResponse Response schema for favorite seller list
type GetFavoriteSellersResponse struct {
	// Items List of favorite sellers
	Items *[]Seller `json:"items,omitempty"`
}

// GetMessagesResponse defines model for GetMessagesResponse.
type GetMessagesResponse struct {
	Items *[]Message `json:"items,omitempty"`
}

// GetNotificationsResponse defines model for GetNotificationsResponse.
type GetNotificationsResponse struct {
	// Items List of notifications for the user
	Items *[]Notification `json:"items,omitempty"`
}

// GetProductCategoriesResponse Response schema for retrieving a list of product categories
type GetProductCategoriesResponse struct {
	// Items List of available product categories
	Items *[]ProductCategory `json:"items,omitempty"`
}

// GetStatusOrderDetailResponse Get result status order detail
type GetStatusOrderDetailResponse struct {
	Result *bool `json:"result,omitempty"`
}

// GetThreadByIdResponse defines model for GetThreadByIdResponse.
type GetThreadByIdResponse struct {
	CreatedAt *string    `json:"createdAt,omitempty"`
	Id        *string    `json:"id,omitempty"`
	Messages  *[]Message `json:"messages,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller    *Seller `json:"seller,omitempty"`
	UpdatedAt *string `json:"updatedAt,omitempty"`
	User      *User   `json:"user,omitempty"`
	Users     *[]User `json:"users,omitempty"`
}

// GetThreadsResponse defines model for GetThreadsResponse.
type GetThreadsResponse struct {
	Items *[]Thread `json:"items,omitempty"`
}

// GetVaultTokensResponse Vault token response
type GetVaultTokensResponse struct {
	// VaultToken Vault token information
	VaultToken *VaultToken `json:"vaultToken,omitempty"`
}

// HomeFeedsResponse Response schema for the timeline feed
type HomeFeedsResponse struct {
	// BannerProducts List of body banners
	BannerProducts *[]BannerProducts `json:"bannerProducts,omitempty"`

	// FavoriteSellersProducts List of favorite sellers products
	FavoriteSellersProducts *[]Product            `json:"favoriteSellersProducts,omitempty"`
	KeywordProducts         *KeywordProducts      `json:"keywordProducts,omitempty"`
	TargetProducts          *map[string][]Product `json:"targetProducts,omitempty"`

	// TopBanners List of top banners
	TopBanners *[]Banner `json:"topBanners,omitempty"`
}

// Inquiry defines model for Inquiry.
type Inquiry struct {
	CategoryCode InquiryCategory `json:"categoryCode"`
	Content      InquiryContent  `json:"content"`
	CreatedAt    *string         `json:"createdAt,omitempty"`
	Id           *string         `json:"id,omitempty"`
	Status       InquiryStatus   `json:"status"`
	UpdatedAt    *string         `json:"updatedAt,omitempty"`
}

// InquiryCategory defines model for InquiryCategory.
type InquiryCategory string

// InquiryContent defines model for InquiryContent.
type InquiryContent struct {
	CountryInquiry *InquiryContentCountry `json:"countryInquiry,omitempty"`
	MessageInquiry *InquiryContentMessage `json:"messageInquiry,omitempty"`
}

// InquiryContentCountry defines model for InquiryContentCountry.
type InquiryContentCountry struct {
	// City Stores city when category code is ‘areaService’
	City *string `json:"city,omitempty"`

	// Country Stores country when category code is ‘areaService’
	Country *string `json:"country,omitempty"`

	// State Stores state when category code is ‘areaService’
	State *string `json:"state,omitempty"`
}

// InquiryContentMessage defines model for InquiryContentMessage.
type InquiryContentMessage struct {
	// AccountId Stores account ID when category code is not ‘areaService’
	AccountId *string `json:"accountId,omitempty"`

	// EmailAddress Stores email address when category code is not ‘areaService’
	EmailAddress *string `json:"emailAddress,omitempty"`

	// FullName Stores full name when category code is not ‘areaService’
	FullName *string `json:"fullName,omitempty"`

	// Message Stores message when category code is not ‘areaService’
	Message *string `json:"message,omitempty"`
}

// InquiryCreateRequest Inquiry request object
type InquiryCreateRequest struct {
	CategoryCode InquiryCategory `json:"categoryCode"`
	Content      InquiryContent  `json:"content"`
}

// InquiryStatus defines model for InquiryStatus.
type InquiryStatus string

// KeywordProducts defines model for KeywordProducts.
type KeywordProducts struct {
	Keyword  *string    `json:"keyword,omitempty"`
	Products *[]Product `json:"products,omitempty"`
}

// Kyc Validation model for indentity information
type Kyc struct {
	// KycImgUrl Url to KYC image (S3, CDN, or any...)
	KycImgUrl *string `json:"kycImgUrl,omitempty"`

	// KycType Proof of indentity
	KycType *KycKycType `json:"kycType,omitempty"`
}

// KycKycType Proof of indentity
type KycKycType string

// MaintenanceDescription defines model for MaintenanceDescription.
type MaintenanceDescription struct {
	Language string `json:"language" validate:"notEmpty"`
	Message  string `json:"message" validate:"notEmpty"`
}

// Message defines model for Message.
type Message struct {
	Attachments *[]Attachment `json:"attachments,omitempty"`
	Content     *string       `json:"content,omitempty"`
	CreatedAt   *string       `json:"createdAt,omitempty"`
	Id          *string       `json:"id,omitempty"`
	Product     *Product      `json:"product,omitempty"`
	ProductId   *string       `json:"productId,omitempty"`
	ReadAt      *string       `json:"readAt,omitempty"`
	UserId      *string       `json:"userId,omitempty"`
}

// MonthlySalesSummaryResponse defines model for MonthlySalesSummaryResponse.
type MonthlySalesSummaryResponse struct {
	// DailyBreakdown Daily breakdown for the month (optional)
	DailyBreakdown *[]DailySalesSummaryResponse `json:"dailyBreakdown,omitempty"`

	// Month Month of the summary (1-12)
	Month int `json:"month"`

	// OrderAmounts Order amount metrics
	OrderAmounts SalesSummaryOrderAmounts `json:"orderAmounts"`

	// OrderAmountsTaxIncluded Order amount metrics
	OrderAmountsTaxIncluded SalesSummaryOrderAmounts `json:"orderAmountsTaxIncluded"`

	// OrderCounts Order count metrics
	OrderCounts SalesSummaryOrderCounts `json:"orderCounts"`

	// SalesAmounts Order amount metrics
	SalesAmounts SalesSummaryOrderAmounts `json:"salesAmounts"`

	// SalesFee Total sales fee for the month
	SalesFee float64 `json:"salesFee"`

	// Vat Total VAT amount for the month
	Vat float64 `json:"vat"`

	// Year Year of the summary
	Year int `json:"year"`
}

// Notification Notification model representing a single notification
type Notification struct {
	ArticleImages *map[string][]string `json:"articleImages,omitempty"`

	// CreatedAt Timestamp when the notification was created
	CreatedAt *time.Time `json:"createdAt,omitempty"`

	// Data Additional data associated with the notification
	Data *map[string]string `json:"data,omitempty"`

	// Icon Icon of the notification
	Icon *string `json:"icon,omitempty"`

	// Id Unique identifier for the notification
	Id *string `json:"id,omitempty"`

	// IsRead Indicates if the notification has been read
	IsRead   *bool              `json:"isRead,omitempty"`
	KeyImage *map[string]string `json:"keyImage,omitempty"`

	// Message Notification message
	Message *map[string]string `json:"message,omitempty"`

	// SystemType System type of the notification
	SystemType *NotificationSystemType `json:"systemType,omitempty"`

	// Title Title of the notification
	Title *map[string]string `json:"title,omitempty"`

	// Type Type of the notification
	Type *NotificationType `json:"type,omitempty"`

	// Url URL of the notification
	Url *string `json:"url,omitempty"`
}

// NotificationSystemType System type of the notification
type NotificationSystemType string

// NotificationType Type of the notification
type NotificationType string

// NotificationSettingsRequest defines model for NotificationSettingsRequest.
type NotificationSettingsRequest struct {
	EmailNotifications *EmailNotificationSettings `json:"emailNotifications,omitempty"`
	PushNotifications  *PushNotificationSettings  `json:"pushNotifications,omitempty"`
}

// NotificationSettingsResponse defines model for NotificationSettingsResponse.
type NotificationSettingsResponse struct {
	EmailNotifications *EmailNotificationSettings `json:"emailNotifications,omitempty"`
	PushNotifications  *PushNotificationSettings  `json:"pushNotifications,omitempty"`
}

// Order defines model for Order.
type Order struct {
	AdministrativeFee          *float64 `json:"administrativeFee,omitempty"`
	AdministrativeFeeRate      *float64 `json:"administrativeFeeRate,omitempty"`
	AdministrativeFeeTaxAmount *float64 `json:"administrativeFeeTaxAmount,omitempty"`
	AdministrativeFeeTaxRate   *float64 `json:"administrativeFeeTaxRate,omitempty"`
	Amount                     *float64 `json:"amount,omitempty"`
	Buyer                      *User    `json:"buyer,omitempty"`
	CreatedAt                  *string  `json:"createdAt,omitempty"`
	Id                         *string  `json:"id,omitempty"`

	// IsUserBlockedBySeller Whether the seller has blocked this buyer
	IsUserBlockedBySeller *bool            `json:"isUserBlockedBySeller,omitempty"`
	Items                 *[]PurchasedItem `json:"items,omitempty"`
	OrderNumber           *string          `json:"orderNumber,omitempty"`
	PaymentMethod         *CreditCard      `json:"paymentMethod,omitempty"`
	PurchaseFee           *float64         `json:"purchaseFee,omitempty"`
	PurchaseFeeTaxAmount  *float64         `json:"purchaseFeeTaxAmount,omitempty"`
	PurchaseFeeTaxRate    *float64         `json:"purchaseFeeTaxRate,omitempty"`
	ShippingAddress       *Address         `json:"shippingAddress,omitempty"`
	TaxAmount             *float64         `json:"taxAmount,omitempty"`

	// TaxInfo Tax information for an order
	TaxInfo     *TaxInfo `json:"taxInfo,omitempty"`
	TaxRate     *float64 `json:"taxRate,omitempty"`
	TotalAmount *float64 `json:"totalAmount,omitempty"`
	UpdatedAt   *string  `json:"updatedAt,omitempty"`
}

// OverallSalesSummaryResponse defines model for OverallSalesSummaryResponse.
type OverallSalesSummaryResponse struct {
	// FinalizedSalesAmount Total finalized sales amount (seller revenue after fees)
	FinalizedSalesAmount float64 `json:"finalizedSalesAmount"`

	// ItemPrice Total item price (product value)
	ItemPrice float64 `json:"itemPrice"`

	// LastFourQuarters Summary for the last 4 quarters with domestic/international breakdown
	LastFourQuarters *[]SalesSummaryQuarterlySummary `json:"lastFourQuarters,omitempty"`

	// SalesFee Total sales fee across all time
	SalesFee float64 `json:"salesFee"`

	// TotalOrderAmount Total order amount across all time
	TotalOrderAmount float64 `json:"totalOrderAmount"`

	// TotalOrdersCount Total number of orders across all time
	TotalOrdersCount int `json:"totalOrdersCount"`

	// Vat Total VAT amount across all time
	Vat float64 `json:"vat"`

	// YearlyBreakdown Yearly breakdown (optional)
	YearlyBreakdown *[]YearlySalesSummaryResponse `json:"yearlyBreakdown,omitempty"`
}

// Pagination defines model for Pagination.
type Pagination struct {
	// Limit number of items per page
	Limit int `json:"limit" validate:"required,gte=1"`

	// Page number of selected page
	Page int `json:"page" validate:"required,gte=1"`
}

// PaymentSourceRequest The payment method to vault with the instrument details
type PaymentSourceRequest struct {
	// Card A Resource representing a request to vault a Card
	Card *CardRequest `json:"card,omitempty"`
}

// PaymentToken The tokenized payment source representing a request to vault a token
type PaymentToken struct {
	// Id The PayPal-generated ID for the token
	Id string `json:"id"`

	// Type The tokenization method that generated the ID
	Type PaymentTokenType `json:"type"`
}

// PaymentTokenType The tokenization method that generated the ID
type PaymentTokenType string

// PaymentTokenSource The payment source for creating a payment token
type PaymentTokenSource struct {
	// Token The tokenized payment source representing a request to vault a token
	Token PaymentToken `json:"token"`
}

// Phone defines model for Phone.
type Phone struct {
	// CountryCode The country code of the phone number without the plus sign (+) (e.g. in Japan it is 81)
	CountryCode string `json:"countryCode"`
	Number      string `json:"number"`
}

// Pin Admin PIN for additional security features
type Pin = string

// PostalCodesResponse Response containing list of valid US postal codes
type PostalCodesResponse struct {
	// Count Total number of postal codes returned
	Count int `json:"count"`

	// PostalCodes Array of valid US postal codes (5-digit ZIP codes)
	PostalCodes []string `json:"postalCodes"`
}

// Product defines model for Product.
type Product struct {
	Brand *ProductBrand `json:"brand,omitempty"`

	// BrandId Brand of the product
	BrandId *string `json:"brandId,omitempty"`

	// BrandNameOther Brand other of the product in case of not in the list
	BrandNameOther *string            `json:"brandNameOther,omitempty"`
	Categories     *[]ProductCategory `json:"categories,omitempty"`

	// CategoryId Category of the product
	CategoryId *string           `json:"categoryId,omitempty"`
	Condition  *ProductCondition `json:"condition,omitempty"`

	// Description Detailed description of the product
	Description *map[string]string `json:"description,omitempty"`

	// DiscountAmount Discount amount applied to the product
	DiscountAmount *float64 `json:"discountAmount,omitempty"`

	// DiscountRate Discount percentage applied to the product
	DiscountRate *float64 `json:"discountRate,omitempty"`

	// Id The unique identifier for the product
	Id *string `json:"id,omitempty"`

	// Images List of product images
	Images *[]string `json:"images,omitempty"`

	// IsDiscount Does the product have a discount?
	IsDiscount *bool `json:"isDiscount,omitempty"`

	// IsFavorite Whether the product is in the user's favorites
	IsFavorite *bool `json:"isFavorite,omitempty"`

	// Name Name of the product
	Name *map[string]string `json:"name,omitempty"`

	// NumFavorites Number of favorites for the product
	NumFavorites *int `json:"numFavorites,omitempty"`

	// Price Price of the product
	Price *float64 `json:"price,omitempty"`

	// ReleaseDate Timestamp of when the product was released
	ReleaseDate *string             `json:"releaseDate,omitempty"`
	SalesStatus *ProductSalesStatus `json:"salesStatus,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller   *Seller `json:"seller,omitempty"`
	SellerId *string `json:"sellerId,omitempty"`

	// ShipFrom Country of the product
	ShipFrom *string `json:"shipFrom,omitempty"`

	// ShippingMethod Shipping method of the product
	ShippingMethod *string `json:"shippingMethod,omitempty"`

	// Size Size information (if applicable)
	Size *string `json:"size,omitempty"`

	// SizeDetail Size detail of the product
	SizeDetail *string `json:"sizeDetail,omitempty"`

	// SizeLange Size lange
	SizeLange         *int                      `json:"sizeLange,omitempty"`
	Target            *ProductTarget            `json:"target,omitempty"`
	TransactionStatus *ProductTransactionStatus `json:"transactionStatus,omitempty"`
}

// ProductBrand defines model for ProductBrand.
type ProductBrand struct {
	DisplayName *map[string]string `json:"displayName,omitempty"`
	Id          *string            `json:"id,omitempty"`
}

// ProductCategory defines model for ProductCategory.
type ProductCategory struct {
	AvailableSizes *map[string][]ProductSize `json:"availableSizes,omitempty"`
	DisplayName    *map[string]string        `json:"displayName,omitempty"`
	Id             *string                   `json:"id,omitempty"`
	SizeTable      *SizeTable                `json:"sizeTable,omitempty"`
	SubCategories  *[]ProductCategory        `json:"subCategories,omitempty"`
}

// ProductCondition defines model for ProductCondition.
type ProductCondition string

// ProductRequest Product response object
type ProductRequest struct {
	// BrandId Brand of the product
	BrandId *string `json:"brandId,omitempty"`

	// BrandNameOther Brand other of the product in case of not in the list
	BrandNameOther *string `json:"brandNameOther,omitempty"`

	// CategoryId Category of the product
	CategoryId *string `json:"categoryId,omitempty"`

	// Condition Condition of the product
	Condition *ProductRequestCondition `json:"condition,omitempty"`

	// Description Description of the product
	Description *map[string]string `json:"description,omitempty"`

	// DiscountAmount Discount amount applied to the product
	DiscountAmount *float64 `json:"discountAmount,omitempty"`

	// DiscountRate Discount percentage applied to the product
	DiscountRate *float64 `json:"discountRate,omitempty"`

	// Images List of product images
	Images *[]string `json:"images,omitempty"`

	// IsDiscount Does the product have a discount?
	IsDiscount *bool `json:"isDiscount,omitempty"`

	// Name Name of the product
	Name map[string]string `json:"name"`

	// Price Price of the product
	Price float64 `json:"price"`

	// ReleaseDate Timestamp of when the product was released
	ReleaseDate *time.Time `json:"releaseDate,omitempty"`

	// SalesStatus Status of the product
	SalesStatus    *ProductRequestSalesStatus `json:"salesStatus,omitempty"`
	ShippingMethod *string                    `json:"shippingMethod,omitempty"`

	// Size Size information (if applicable)
	Size       *string `json:"size,omitempty"`
	SizeDetail *string `json:"sizeDetail,omitempty"`

	// SizeLange Size lange
	SizeLange *int           `json:"sizeLange,omitempty"`
	Target    *ProductTarget `json:"target,omitempty"`
}

// ProductRequestCondition Condition of the product
type ProductRequestCondition string

// ProductRequestSalesStatus Status of the product
type ProductRequestSalesStatus string

// ProductSalesStatus defines model for ProductSalesStatus.
type ProductSalesStatus string

// ProductSize defines model for ProductSize.
type ProductSize struct {
	Lange int    `json:"lange"`
	Size  string `json:"size"`
}

// ProductTarget defines model for ProductTarget.
type ProductTarget string

// ProductTransactionStatus defines model for ProductTransactionStatus.
type ProductTransactionStatus string

// ProductUpdateRequest Product request update object
type ProductUpdateRequest struct {
	// BrandId Brand of the product
	BrandId *string `json:"brandId,omitempty"`

	// BrandNameOther Brand other of the product in case of not in the list
	BrandNameOther *string `json:"brandNameOther,omitempty"`

	// CategoryId Category of the product
	CategoryId *string           `json:"categoryId,omitempty"`
	Condition  *ProductCondition `json:"condition,omitempty"`

	// Description Description of the product
	Description *map[string]string `json:"description,omitempty"`

	// DiscountAmount Discount amount applied to the product
	DiscountAmount *float64 `json:"discountAmount,omitempty"`

	// DiscountRate Discount percentage applied to the product
	DiscountRate *float64 `json:"discountRate,omitempty"`

	// Images List of product images
	Images *[]string `json:"images,omitempty"`

	// IsDiscount Does the product have a discount?
	IsDiscount *bool `json:"isDiscount,omitempty"`

	// Name Name of the product
	Name *map[string]string `json:"name,omitempty"`

	// Price Price of the product
	Price *float64 `json:"price,omitempty"`

	// ReleaseDate Timestamp of when the product was released
	ReleaseDate    *time.Time          `json:"releaseDate,omitempty"`
	SalesStatus    *ProductSalesStatus `json:"salesStatus,omitempty"`
	ShippingMethod *string             `json:"shippingMethod,omitempty"`

	// Size Size information (if applicable)
	Size       *string `json:"size,omitempty"`
	SizeDetail *string `json:"sizeDetail,omitempty"`

	// SizeLange Size lange
	SizeLange *int           `json:"sizeLange,omitempty"`
	Target    *ProductTarget `json:"target,omitempty"`
}

// PurchaseOrderRequest defines model for PurchaseOrderRequest.
type PurchaseOrderRequest struct {
	AccessId *string `json:"accessId,omitempty"`
}

// PurchasedItem defines model for PurchasedItem.
type PurchasedItem struct {
	Amount         *float64   `json:"amount,omitempty"`
	CompletedDate  *time.Time `json:"completedDate,omitempty"`
	CreatedAt      *time.Time `json:"createdAt,omitempty"`
	DiscountAmount *float64   `json:"discountAmount,omitempty"`
	Id             *string    `json:"id,omitempty"`
	Note           *string    `json:"note,omitempty"`
	Order          *Order     `json:"order,omitempty"`
	OrderEnd       *time.Time `json:"orderEnd,omitempty"`
	OrderId        *string    `json:"orderId,omitempty"`
	OrderNumber    *string    `json:"orderNumber,omitempty"`
	Product        *Product   `json:"product,omitempty"`
	ProductId      *string    `json:"productId,omitempty"`
	ReceivedDate   *time.Time `json:"receivedDate,omitempty"`

	// SalesFeeInfo Sales fee information for an order detail
	SalesFeeInfo *SalesFeeInfo `json:"salesFeeInfo,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller            *Seller                   `json:"seller,omitempty"`
	ShippedDate       *time.Time                `json:"shippedDate,omitempty"`
	TaxAmount         *float64                  `json:"taxAmount,omitempty"`
	TaxDetails        *[]TaxDetail              `json:"taxDetails,omitempty"`
	TaxInfos          *[]TaxItemInfo            `json:"taxInfos,omitempty"`
	TaxRate           *float64                  `json:"taxRate,omitempty"`
	TotalAmount       *float64                  `json:"totalAmount,omitempty"`
	TransactionStatus *ProductTransactionStatus `json:"transactionStatus,omitempty"`
	WaybillNumber     *string                   `json:"waybillNumber,omitempty"`
}

// PurchasedItemsResponse defines model for PurchasedItemsResponse.
type PurchasedItemsResponse struct {
	Items *[]PurchasedItem `json:"items,omitempty"`
}

// PushNotificationSettings defines model for PushNotificationSettings.
type PushNotificationSettings struct {
	// CampaignsAndPromotions Get updates on special offers, deals, and promotions.
	CampaignsAndPromotions *bool `json:"campaignsAndPromotions,omitempty"`

	// CartAndFavorites Get alerts on price changes and stock updates.
	CartAndFavorites *bool `json:"cartAndFavorites,omitempty"`

	// FavoritedShops Get notified when someone favorites your shop.
	FavoritedShops *bool `json:"favoritedShops,omitempty"`

	// ImportantNotices Get important updates about your account and services.
	ImportantNotices *bool `json:"importantNotices,omitempty"`

	// ListedItems Get updates when your listed items are purchased.
	ListedItems *bool `json:"listedItems,omitempty"`

	// Messages Get notifications when you receive a new message.
	Messages *bool `json:"messages,omitempty"`

	// OrderStatus Get updates on shipping and purchase status.
	OrderStatus *bool `json:"orderStatus,omitempty"`

	// StopAll Temporarily stop all push notifications from this app.
	StopAll *bool `json:"stopAll,omitempty"`

	// Withdrawals Get updates on your withdrawal request status.
	Withdrawals *bool `json:"withdrawals,omitempty"`
}

// RatingSellerResponse Get rating seller response object
type RatingSellerResponse struct {
	AvgDaysToShip   *float64 `json:"avgDaysToShip,omitempty"`
	AvgPackRating   *float64 `json:"avgPackRating,omitempty"`
	AvgPoliteRating *float64 `json:"avgPoliteRating,omitempty"`
	AvgRating       *float64 `json:"avgRating,omitempty"`
	AvgSpeedRating  *float64 `json:"avgSpeedRating,omitempty"`
	TotalOrders     *int     `json:"totalOrders,omitempty"`
}

// Region defines model for Region.
type Region struct {
	Code        *string            `json:"code,omitempty"`
	DisplayName *map[string]string `json:"displayName,omitempty"`
	Id          *string            `json:"id,omitempty"`
	SubRegions  *[]Region          `json:"subRegions,omitempty"`
}

// RegisterSellerRequest Request schema for enabling seller functionality
type RegisterSellerRequest struct {
	Address *Address `json:"address,omitempty"`

	// BankAccount Validation model for bank account profile information
	BankAccount *BankAccount `json:"bankAccount,omitempty"`

	// CompanyInfo Validation model for indentity information
	CompanyInfo *CompanyInfo `json:"companyInfo,omitempty"`

	// Kyc Validation model for indentity information
	Kyc *Kyc `json:"kyc,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller Seller `json:"seller"`
}

// RegisterUserRequest defines model for RegisterUserRequest.
type RegisterUserRequest struct {
	BillingAddress *Address `json:"billingAddress,omitempty"`

	// CountryCode Country code of the user
	CountryCode *string `json:"countryCode,omitempty"`

	// CreditCardAccessId Credit card access ID of the user
	CreditCardAccessId *string `json:"creditCardAccessId,omitempty"`

	// DateOfBirth Date of birth of the user. Format: YYYY-MM-DD
	DateOfBirth *string `json:"dateOfBirth,omitempty"`

	// Email User's email address
	Email *string `json:"email,omitempty"`

	// FirstName First name of the user
	FirstName *string `json:"firstName,omitempty"`

	// Gender Gender of the user
	Gender      *string  `json:"gender,omitempty"`
	HomeAddress *Address `json:"homeAddress,omitempty"`

	// Id User's unique identifier
	Id *string `json:"id,omitempty"`

	// Lang Language of the user
	Lang *string `json:"lang,omitempty"`

	// LastName Last name of the user
	LastName *string `json:"lastName,omitempty"`

	// Nickname Nickname of the user
	Nickname *string `json:"nickname,omitempty"`

	// Password Password of the user
	Password string `json:"password"`

	// ReceiveNewsletter Whether the user wants to receive newsletter
	ReceiveNewsletter *bool `json:"receiveNewsletter,omitempty"`

	// RegionId Region ID of the user
	RegionId *string `json:"regionId,omitempty"`

	// RegisterSellerRequest Request schema for enabling seller functionality
	RegisterSellerRequest *RegisterSellerRequest `json:"registerSellerRequest,omitempty"`
	ShippingAddress       *Address               `json:"shippingAddress,omitempty"`
}

// Report defines model for Report.
type Report struct {
	CreatedAt    *string             `json:"createdAt,omitempty"`
	Detail       *string             `json:"detail,omitempty"`
	Id           *string             `json:"id,omitempty"`
	Item         *Report_Item        `json:"item,omitempty"`
	ItemId       *string             `json:"itemId,omitempty"`
	ItemType     *ReportItemType     `json:"itemType,omitempty"`
	Reporter     *Report_Reporter    `json:"reporter,omitempty"`
	ReporterId   *string             `json:"reporterId,omitempty"`
	ReporterType *ReportReporterType `json:"reporterType,omitempty"`
	Status       *ReportStatus       `json:"status,omitempty"`
	UpdatedAt    *string             `json:"updatedAt,omitempty"`
}

// Report_Item defines model for Report.Item.
type Report_Item struct {
	union json.RawMessage
}

// ReportItemType defines model for Report.ItemType.
type ReportItemType string

// Report_Reporter defines model for Report.Reporter.
type Report_Reporter struct {
	union json.RawMessage
}

// ReportReporterType defines model for Report.ReporterType.
type ReportReporterType string

// ReportStatus defines model for ReportStatus.
type ReportStatus string

// ResetPasswordConfirmRequest Request to confirm new password
type ResetPasswordConfirmRequest struct {
	ConfirmPassword string `json:"confirmPassword" validate:"required,eqfield=NewPassword"`
	NewPassword     string `json:"newPassword" validate:"required,min=8"`
	Token           string `json:"token" validate:"required,min=1"`
}

// ResetPasswordRequestRequest Request to initiate password reset
type ResetPasswordRequestRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// ResetPasswordVerifyRequest Request to verify reset password token
type ResetPasswordVerifyRequest struct {
	Token string `json:"token" validate:"required,min=1"`
}

// SalesFeeInfo Sales fee information for an order detail
type SalesFeeInfo struct {
	// SalesFee The sales fee amount charged to the seller
	SalesFee *float64 `json:"salesFee,omitempty"`

	// SalesFeeRate The sales fee rate percentage
	SalesFeeRate *float64 `json:"salesFeeRate,omitempty"`

	// SalesTaxAmount The tax amount on the sales fee
	SalesTaxAmount *float64 `json:"salesTaxAmount,omitempty"`

	// SalesTaxInfos Detailed tax information for the sales fee
	SalesTaxInfos *[]TaxItemInfo `json:"salesTaxInfos,omitempty"`

	// SalesTaxRate The tax rate applied to the sales fee
	SalesTaxRate *float64 `json:"salesTaxRate,omitempty"`
}

// SalesInsightsResponse defines model for SalesInsightsResponse.
type SalesInsightsResponse struct {
	// FollowerTrends Follower statistics and trends
	FollowerTrends FollowerTrends `json:"followerTrends"`
}

// SalesSummaryOrderAmounts Order amount metrics
type SalesSummaryOrderAmounts struct {
	// Domestic Domestic amount
	Domestic float64 `json:"domestic"`

	// International International amount
	International float64 `json:"international"`

	// Total Total amount
	Total float64 `json:"total"`
}

// SalesSummaryOrderCounts Order count metrics
type SalesSummaryOrderCounts struct {
	// Domestic Number of domestic orders
	Domestic int `json:"domestic"`

	// International Number of international orders
	International int `json:"international"`

	// Total Total number of orders
	Total int `json:"total"`
}

// SalesSummaryOrderDetail defines model for SalesSummaryOrderDetail.
type SalesSummaryOrderDetail struct {
	// OrderAmount The amount of the order
	OrderAmount *float32 `json:"orderAmount,omitempty"`

	// OrderId The ID of the order
	OrderId *string `json:"orderId,omitempty"`

	// OrderNumber The number of the order
	OrderNumber *string `json:"orderNumber,omitempty"`
}

// SalesSummaryQuarterlySummary Quarterly sales summary with domestic/international breakdown
type SalesSummaryQuarterlySummary struct {
	// OrderAmounts Order amount metrics
	OrderAmounts SalesSummaryOrderAmounts `json:"orderAmounts"`

	// OrderCounts Order count metrics
	OrderCounts SalesSummaryOrderCounts `json:"orderCounts"`

	// Quarter Quarter number (1-4)
	Quarter int `json:"quarter"`

	// Year Year of the quarter
	Year int `json:"year"`
}

// SalesSummarySimpleOrderTotals Simple order totals without domestic/international breakdown
type SalesSummarySimpleOrderTotals struct {
	// TotalOrderAmount Total order amount
	TotalOrderAmount float64 `json:"totalOrderAmount"`

	// TotalOrdersCount Total number of orders
	TotalOrdersCount int `json:"totalOrdersCount"`
}

// SearchProductsRequest Search products request object
type SearchProductsRequest struct {
	// BrandId Brand ID to search for
	BrandId *string `json:"brandId,omitempty"`

	// BrandIds Brand IDs to search for
	BrandIds *[]string `json:"brandIds,omitempty"`

	// BrandNameOther Brand name to search for
	BrandNameOther *string `json:"brandNameOther,omitempty"`

	// CategoryId Category ID to search for
	CategoryId *string `json:"categoryId,omitempty"`

	// CategoryIds Category IDs to search for
	CategoryIds *[]string `json:"categoryIds,omitempty"`

	// Conditions Conditions to search for
	Conditions *[]ProductCondition `json:"conditions,omitempty"`

	// CountryCode Country code to search for
	CountryCode *string `json:"countryCode,omitempty"`

	// CountryCodes Country codes to search for
	CountryCodes *[]string `json:"countryCodes,omitempty"`

	// IsDiscount Whether to search for discount products
	IsDiscount *bool `json:"isDiscount,omitempty"`

	// IsOnlyDiscount Whether to search for only discount products
	IsOnlyDiscount *bool `json:"isOnlyDiscount,omitempty"`

	// IsSold Whether to search for sold products
	IsSold *bool `json:"isSold,omitempty"`

	// IsUpcoming Whether to search for upcoming products
	IsUpcoming *bool `json:"isUpcoming,omitempty"`

	// Keyword Keyword to search for
	Keyword *string `json:"keyword,omitempty"`

	// MaxPrice Maximum price to search for
	MaxPrice *float64 `json:"maxPrice,omitempty"`

	// MinPrice Minimum price to search for
	MinPrice   *float64    `json:"minPrice,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`

	// RegionId Region ID to search for
	RegionId *string `json:"regionId,omitempty"`

	// SellerId Seller ID to search for
	SellerId *string `json:"sellerId,omitempty"`

	// Size Size to search for
	Size *string `json:"size,omitempty"`

	// SizeLanges Size langes to search for
	SizeLanges *[]int `json:"sizeLanges,omitempty"`

	// Sizes Sizes to search for
	Sizes *[]string `json:"sizes,omitempty"`

	// Sort Sort by
	Sort   *SearchProductsRequestSort `json:"sort,omitempty"`
	Target *ProductTarget             `json:"target,omitempty"`

	// Targets Targets to search for
	Targets *[]ProductTarget `json:"targets,omitempty"`
}

// SearchProductsRequestSort Sort by
type SearchProductsRequestSort string

// SearchProductsResponse Search products response object
type SearchProductsResponse struct {
	Products *[]Product `json:"products,omitempty"`
}

// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
type Seller struct {
	// About Something about shop
	About *map[string]string `json:"about,omitempty"`

	// AccountId Account ID
	AccountId *string `json:"accountId,omitempty"`

	// AccountType Validated account types
	AccountType SellerAccountType `json:"accountType"`

	// AvatarUrl Url to avatar (S3, CDN, or any...)
	AvatarUrl *string `json:"avatarUrl,omitempty"`

	// CountryCode Country code
	CountryCode *string `json:"countryCode,omitempty"`

	// FavoriteBrands List of favorite brands
	FavoriteBrands *[]string `json:"favoriteBrands,omitempty"`

	// HeaderImgUrl Url to header image (S3, CDN, or any...)
	HeaderImgUrl *string `json:"headerImgUrl,omitempty"`

	// Id Seller ID
	Id *string `json:"id,omitempty"`

	// IsFavorite Whether the seller is a favorite of the user
	IsFavorite *bool `json:"isFavorite,omitempty"`

	// NumFavorites Number of favorites
	NumFavorites *int `json:"numFavorites,omitempty"`

	// RegionId Region id
	RegionId *string `json:"regionId,omitempty"`

	// ShopName Shop name
	ShopName string `json:"shopName"`

	// Specialty Specialty
	Specialty *map[string]string `json:"specialty,omitempty"`

	// StockLocation Stock locaiton. (Ship from)
	StockLocation *string `json:"stockLocation,omitempty"`
}

// SellerAccountType Validated account types
type SellerAccountType string

// SellerDashboardResponse Response schema for the seller dashboard
type SellerDashboardResponse struct {
	// Yesterday Response schema for the shop results
	Yesterday *ShopResult `json:"yesterday,omitempty"`
}

// SellerProductsResponse Get products response object
type SellerProductsResponse struct {
	Products *[]Product `json:"products,omitempty"`
}

// SellerProfileResponse Seller info
type SellerProfileResponse struct {
	Address *Address `json:"address,omitempty"`

	// BankAccount Validation model for bank account profile information
	BankAccount *BankAccount `json:"bankAccount,omitempty"`

	// CompanyInfo Validation model for indentity information
	CompanyInfo *CompanyInfo `json:"companyInfo,omitempty"`

	// Kyc Validation model for indentity information
	Kyc *Kyc `json:"kyc,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller Seller `json:"seller"`
}

// SendMessageRequest defines model for SendMessageRequest.
type SendMessageRequest struct {
	// Attachments The attachments of the message
	Attachments *[]Attachment `json:"attachments,omitempty"`

	// Content The content of the message
	Content string `json:"content"`

	// ProductId The id of the product
	ProductId *string `json:"productId,omitempty"`

	// ThreadId The id of the thread
	ThreadId string `json:"threadId"`
}

// SettingPinRequest Request schema for setting up admin PIN
type SettingPinRequest struct {
	// Pin Admin PIN for additional security features
	Pin Pin `json:"pin"`
}

// ShopResult Response schema for the shop results
type ShopResult struct {
	OrderAmountTaxExcluded *float64 `json:"orderAmountTaxExcluded,omitempty"`
	OrderAmountTaxIncluded *float64 `json:"orderAmountTaxIncluded,omitempty"`
	ProductFavorites       *int     `json:"productFavorites,omitempty"`
	SalesAmount            *float64 `json:"salesAmount,omitempty"`
	ShopFavorites          *int     `json:"shopFavorites,omitempty"`
	Views                  *int     `json:"views,omitempty"`
}

// SignupRequest Signup Request
type SignupRequest struct {
	// CountryCode Country code
	CountryCode *string `json:"countryCode,omitempty"`

	// Email Email
	Email string `json:"email" validate:"required,email"`

	// Lang Language
	Lang *string `json:"lang,omitempty"`

	// ReceiveNewsletter Agree to receive news
	ReceiveNewsletter *bool `json:"receiveNewsletter,omitempty"`

	// RegionId Region id
	RegionId *string `json:"regionId,omitempty"`

	// RegisterType Register type
	RegisterType *SignupRequestRegisterType `json:"registerType,omitempty"`
}

// SignupRequestRegisterType Register type
type SignupRequestRegisterType string

// SignupVerifyRequest Verify signup token
type SignupVerifyRequest struct {
	Token string `json:"token" validate:"required,min=1"`
}

// SizeTable defines model for SizeTable.
type SizeTable string

// StatusResponse defines model for StatusResponse.
type StatusResponse struct {
	// ForceUpdateAndroid Whether user must update app to continue for Android
	ForceUpdateAndroid bool `json:"forceUpdateAndroid"`

	// ForceUpdateIOS Whether user must update app to continue for iOS
	ForceUpdateIOS bool `json:"forceUpdateIOS"`

	// IsMaintenance Whether app is in maintenance mode
	IsMaintenance bool `json:"isMaintenance"`

	// LatestVersionAndroid Latest app version available for Android
	LatestVersionAndroid string `json:"latestVersionAndroid"`

	// LatestVersionIOS Latest app version available for iOS
	LatestVersionIOS string `json:"latestVersionIOS"`

	// MaintenanceDescription Maintenance description
	MaintenanceDescription *[]MaintenanceDescription `json:"maintenanceDescription,omitempty"`

	// Message Additional status message
	Message *string `json:"message,omitempty"`

	// NetworkStatus Network connection status
	NetworkStatus bool `json:"networkStatus"`

	// ServerStatus Backend server status
	ServerStatus bool `json:"serverStatus"`
}

// Subscription defines model for Subscription.
type Subscription struct {
	AutoRenewEnabled   *bool      `json:"autoRenewEnabled,omitempty"`
	ExpireDate         *time.Time `json:"expireDate,omitempty"`
	Id                 *string    `json:"id,omitempty"`
	Platform           *string    `json:"platform,omitempty"`
	StartDate          *time.Time `json:"startDate,omitempty"`
	SubscriptionStatus *string    `json:"subscriptionStatus,omitempty"`
	TransactionId      *string    `json:"transactionId,omitempty"`
	UserId             *string    `json:"userId,omitempty"`
}

// SubscriptionMembershipRequest defines model for SubscriptionMembershipRequest.
type SubscriptionMembershipRequest struct {
	Platform       string `json:"platform"`
	PurchaseToken  string `json:"purchaseToken"`
	SubscriptionID string `json:"subscriptionID"`
	TransactionID  string `json:"transactionID"`
}

// TaxDetail defines model for TaxDetail.
type TaxDetail struct {
	Amount    *float64 `json:"amount,omitempty"`
	TaxAmount *float64 `json:"taxAmount,omitempty"`
	TaxRate   *float64 `json:"taxRate,omitempty"`
}

// TaxInfo Tax information for an order
type TaxInfo struct {
	Country    *Country     `json:"country,omitempty"`
	Regions    *[]Region    `json:"regions,omitempty"`
	TaxDetails *[]TaxDetail `json:"taxDetails,omitempty"`
	TaxName    *TaxName     `json:"taxName,omitempty"`
}

// TaxItemInfo Tax information for an order
type TaxItemInfo struct {
	Country    *Country     `json:"country,omitempty"`
	District   *Region      `json:"district,omitempty"`
	Region     *Region      `json:"region,omitempty"`
	TaxDetails *[]TaxDetail `json:"taxDetails,omitempty"`
	TaxName    *TaxName     `json:"taxName,omitempty"`
}

// TaxName defines model for TaxName.
type TaxName string

// Term defines model for Term.
type Term struct {
	Content     *TermContent       `json:"content,omitempty"`
	CountryCode *string            `json:"countryCode,omitempty"`
	CreatedAt   *string            `json:"createdAt,omitempty"`
	Id          *string            `json:"id,omitempty"`
	IsCategory  *bool              `json:"isCategory,omitempty"`
	Title       *map[string]string `json:"title,omitempty"`
	UpdatedAt   *string            `json:"updatedAt,omitempty"`
}

// TermContent defines model for TermContent.
type TermContent struct {
	BodyUrl *map[string]string `json:"bodyUrl,omitempty"`

	// IsRequired 同意必須
	IsRequired *bool `json:"isRequired,omitempty"`
}

// TermsResponse defines model for TermsResponse.
type TermsResponse struct {
	Items *[]Term `json:"items,omitempty"`
}

// Thread defines model for Thread.
type Thread struct {
	Id      *string  `json:"id,omitempty"`
	Message *Message `json:"message,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller    *Seller `json:"seller,omitempty"`
	UpdatedAt *string `json:"updatedAt,omitempty"`
	User      *User   `json:"user,omitempty"`
	Users     *[]User `json:"users,omitempty"`
}

// UpdateEmailRequest defines model for UpdateEmailRequest.
type UpdateEmailRequest struct {
	// Email The new email to be set
	Email string `json:"email" validate:"required,email"`
}

// UpdatePasswordRequest defines model for UpdatePasswordRequest.
type UpdatePasswordRequest struct {
	// CurrentPassword The current password of the user.
	CurrentPassword string `json:"currentPassword"`

	// NewPassword The new password to be set.
	NewPassword string `json:"newPassword"`
}

// UpdatePaymentMethodsRequest Request schema for updating user payment methods
type UpdatePaymentMethodsRequest struct {
	// AccessId The access ID of the credit card
	AccessId       string   `json:"accessId"`
	BillingAddress *Address `json:"billingAddress,omitempty"`
}

// UpdatePaymentMethodsResponse defines model for UpdatePaymentMethodsResponse.
type UpdatePaymentMethodsResponse struct {
	CardResult *CreditCard `json:"cardResult,omitempty"`
}

// UpdatePinRequest Request schema for setting up admin PIN
type UpdatePinRequest struct {
	// NewPin Admin PIN for additional security features
	NewPin Pin `json:"newPin"`

	// Pin Admin PIN for additional security features
	Pin Pin `json:"pin"`
}

// UpdatePublishSellerRequest Publish Status Seller request
type UpdatePublishSellerRequest struct {
	// PublicStatus Status of the public
	PublicStatus *UpdatePublishSellerRequestPublicStatus `json:"publicStatus,omitempty"`
}

// UpdatePublishSellerRequestPublicStatus Status of the public
type UpdatePublishSellerRequestPublicStatus string

// UpdateSellerBankAccountRequest Validation model for bank account profile information
type UpdateSellerBankAccountRequest = BankAccount

// UpdateSellerRequest Update seller information
type UpdateSellerRequest struct {
	Address *Address `json:"address,omitempty"`

	// BankAccount Validation model for bank account profile information
	BankAccount *BankAccount `json:"bankAccount,omitempty"`

	// CompanyInfo Validation model for indentity information
	CompanyInfo *CompanyInfo `json:"companyInfo,omitempty"`

	// Kyc Validation model for indentity information
	Kyc *Kyc `json:"kyc,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller *Seller `json:"seller,omitempty"`
}

// UpdateSoldItemStatusRequest defines model for UpdateSoldItemStatusRequest.
type UpdateSoldItemStatusRequest struct {
	TransactionStatus ProductTransactionStatus `json:"transactionStatus"`

	// WaybillNumber Waybill/tracking number (required for shipped and received status)
	WaybillNumber *string `json:"waybillNumber,omitempty"`
}

// UpdateUserProfileRequest Request schema for updating user profile information
type UpdateUserProfileRequest struct {
	BillingAddress  *Address `json:"billingAddress,omitempty"`
	HomeAddress     *Address `json:"homeAddress,omitempty"`
	ShippingAddress *Address `json:"shippingAddress,omitempty"`
	User            *User    `json:"user,omitempty"`
}

// UpdateUserTermsRequest defines model for UpdateUserTermsRequest.
type UpdateUserTermsRequest struct {
	ConfirmTermDate string `json:"confirmTermDate"`
}

// UploadFileRequest defines model for UploadFileRequest.
type UploadFileRequest struct {
	File []openapi_types.File `json:"file"`
}

// UploadFileResponse defines model for UploadFileResponse.
type UploadFileResponse struct {
	// Key The key or path of the file
	// Deprecated:
	Key  string   `json:"key"`
	Keys []string `json:"keys"`
}

// UpsertFCMTokenRequest defines model for UpsertFCMTokenRequest.
type UpsertFCMTokenRequest struct {
	// AppVersion Application version (optional)
	AppVersion *string `json:"appVersion,omitempty"`

	// DeviceId Device identifier (optional)
	DeviceId *string `json:"deviceId,omitempty"`

	// DeviceType Type of device
	DeviceType *UpsertFCMTokenRequestDeviceType `json:"deviceType,omitempty"`

	// FcmToken Firebase Cloud Messaging token
	FcmToken string `json:"fcmToken"`
}

// UpsertFCMTokenRequestDeviceType Type of device
type UpsertFCMTokenRequestDeviceType string

// User defines model for User.
type User struct {
	AccountId *string `json:"accountId,omitempty"`

	// Avatar User's avatar URL
	Avatar          *string `json:"avatar,omitempty"`
	ConfirmTermDate *string `json:"confirmTermDate,omitempty"`

	// CountryCode Country code of the user
	CountryCode *string `json:"countryCode,omitempty"`

	// DateOfBirth Date of birth of the user. Format: YYYY-MM-DD
	DateOfBirth *string `json:"dateOfBirth,omitempty"`

	// Email User's email address
	Email *string `json:"email,omitempty"`

	// FirstName First name of the user
	FirstName *string `json:"firstName,omitempty"`

	// Gender Gender of the user
	Gender *string `json:"gender,omitempty"`

	// Id User's unique identifier
	Id *string `json:"id,omitempty"`

	// IsTermsRequired Whether the user has confirmed the terms
	IsTermsRequired *bool `json:"isTermsRequired,omitempty"`

	// Lang Language of the user
	Lang *string `json:"lang,omitempty"`

	// LastName Last name of the user
	LastName   *string `json:"lastName,omitempty"`
	Membership *int    `json:"membership,omitempty"`

	// Nickname Nickname of the user
	Nickname *string `json:"nickname,omitempty"`

	// PinSetting Whether the user has set a PIN
	PinSetting *bool `json:"pinSetting,omitempty"`

	// ReceiveNewsletter Whether the user wants to receive newsletter
	ReceiveNewsletter *bool `json:"receiveNewsletter,omitempty"`

	// RegionId Region ID of the user
	RegionId *string `json:"regionId,omitempty"`

	// SellerId Seller ID of the user
	SellerId *string `json:"sellerId,omitempty"`
}

// UserProfileResponse Comprehensive user profile response with optional seller profile details
type UserProfileResponse struct {
	BillingAddress *Address `json:"billingAddress,omitempty"`

	// CompanyInfo Validation model for indentity information
	CompanyInfo *CompanyInfo `json:"companyInfo,omitempty"`
	CreditCard  *CreditCard  `json:"creditCard,omitempty"`
	HomeAddress *Address     `json:"homeAddress,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller          *Seller  `json:"seller,omitempty"`
	ShippingAddress *Address `json:"shippingAddress,omitempty"`
	User            User     `json:"user"`
}

// VaultToken Vault token information
type VaultToken struct {
	CardInfo CreditCard `json:"cardInfo"`
}

// VerifyCreditCardRequest defines model for VerifyCreditCardRequest.
type VerifyCreditCardRequest struct {
	Address *Address `json:"address,omitempty"`
	Payer   *User    `json:"payer,omitempty"`

	// Token The token of the credit card
	Token string `json:"token"`
}

// VerifyCreditCardResponse defines model for VerifyCreditCardResponse.
type VerifyCreditCardResponse struct {
	// AccessId The access ID of the credit card
	AccessId *string `json:"accessId,omitempty"`

	// RedirectUrl The redirect URL to the credit card verification page
	RedirectUrl *string `json:"redirectUrl,omitempty"`
}

// VerifyEmailRequest defines model for VerifyEmailRequest.
type VerifyEmailRequest struct {
	// Token The token to verify the email
	Token string `json:"token"`
}

// YearlySalesSummaryResponse defines model for YearlySalesSummaryResponse.
type YearlySalesSummaryResponse struct {
	// MonthlyBreakdown Monthly breakdown for the year (optional)
	MonthlyBreakdown *[]MonthlySalesSummaryResponse `json:"monthlyBreakdown,omitempty"`

	// OrderAmounts Order amount metrics
	OrderAmounts SalesSummaryOrderAmounts `json:"orderAmounts"`

	// OrderAmountsTaxIncluded Order amount metrics
	OrderAmountsTaxIncluded SalesSummaryOrderAmounts `json:"orderAmountsTaxIncluded"`

	// OrderCounts Order count metrics
	OrderCounts SalesSummaryOrderCounts `json:"orderCounts"`

	// SalesAmounts Order amount metrics
	SalesAmounts SalesSummaryOrderAmounts `json:"salesAmounts"`

	// SalesFee Total sales fee for the year
	SalesFee float64 `json:"salesFee"`

	// Vat Total VAT amount for the year
	Vat float64 `json:"vat"`

	// Year Year of the summary
	Year int `json:"year"`
}

// GetBannersParams defines parameters for GetBanners.
type GetBannersParams struct {
	// CountryCode The country code for filter data
	CountryCode *string `form:"countryCode,omitempty" json:"countryCode,omitempty"`
}

// GetBrandsParams defines parameters for GetBrands.
type GetBrandsParams struct {
	Keyword *string `form:"keyword,omitempty" json:"keyword,omitempty"`
}

// GetCountriesParams defines parameters for GetCountries.
type GetCountriesParams struct {
	// Mode Mode
	Mode *GetCountriesParamsMode `form:"mode,omitempty" json:"mode,omitempty"`
}

// GetCountriesParamsMode defines parameters for GetCountries.
type GetCountriesParamsMode string

// GetFaqsParams defines parameters for GetFaqs.
type GetFaqsParams struct {
	// CategoryId Category ID
	CategoryId *string `form:"categoryId,omitempty" json:"categoryId,omitempty"`
}

// GetFollowedSellerProductsParams defines parameters for GetFollowedSellerProducts.
type GetFollowedSellerProductsParams struct {
	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit  *int                                 `form:"limit,omitempty" json:"limit,omitempty"`
	Target *ProductTarget                       `form:"target,omitempty" json:"target,omitempty"`
	Sort   *GetFollowedSellerProductsParamsSort `form:"sort,omitempty" json:"sort,omitempty"`
}

// GetFollowedSellerProductsParamsSort defines parameters for GetFollowedSellerProducts.
type GetFollowedSellerProductsParamsSort string

// GetHomeFeedsParams defines parameters for GetHomeFeeds.
type GetHomeFeedsParams struct {
	// CountryCode Country code
	CountryCode string `form:"countryCode" json:"countryCode"`

	// Target Target
	Target *ProductTarget `form:"target,omitempty" json:"target,omitempty"`

	// Keyword Keyword
	Keyword *string `form:"keyword,omitempty" json:"keyword,omitempty"`
}

// GetProductCategoriesParams defines parameters for GetProductCategories.
type GetProductCategoriesParams struct {
	Keyword  *string `form:"keyword,omitempty" json:"keyword,omitempty"`
	ParentId *string `form:"parentId,omitempty" json:"parentId,omitempty"`
	SellerId *string `form:"sellerId,omitempty" json:"sellerId,omitempty"`
}

// GetBlockUsersParams defines parameters for GetBlockUsers.
type GetBlockUsersParams struct {
	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetFollowingUsersParams defines parameters for GetFollowingUsers.
type GetFollowingUsersParams struct {
	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetSoldItemsParams defines parameters for GetSoldItems.
type GetSoldItemsParams struct {
	From    *string                    `form:"from,omitempty" json:"from,omitempty"`
	To      *string                    `form:"to,omitempty" json:"to,omitempty"`
	OrderBy *GetSoldItemsParamsOrderBy `form:"orderBy,omitempty" json:"orderBy,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetSoldItemsParamsOrderBy defines parameters for GetSoldItems.
type GetSoldItemsParamsOrderBy string

// GetSellerProductsParams defines parameters for GetSellerProducts.
type GetSellerProductsParams struct {
	Page  *int                         `form:"page,omitempty" json:"page,omitempty"`
	Limit *int                         `form:"limit,omitempty" json:"limit,omitempty"`
	Sort  *GetSellerProductsParamsSort `form:"sort,omitempty" json:"sort,omitempty"`
}

// GetSellerProductsParamsSort defines parameters for GetSellerProducts.
type GetSellerProductsParamsSort string

// GetTermsParams defines parameters for GetTerms.
type GetTermsParams struct {
	// CategoryId Category ID
	CategoryId *string `form:"categoryId,omitempty" json:"categoryId,omitempty"`

	// CountryCode Country Code
	CountryCode *string `form:"countryCode,omitempty" json:"countryCode,omitempty"`

	// Type Type
	Type *GetTermsParamsType `form:"type,omitempty" json:"type,omitempty"`

	// Page Page number
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetTermsParamsType defines parameters for GetTerms.
type GetTermsParamsType string

// GetUserThreadsParams defines parameters for GetUserThreads.
type GetUserThreadsParams struct {
	// Status Status of the thread
	Status *GetUserThreadsParamsStatus `form:"status,omitempty" json:"status,omitempty"`

	// Keyword Keyword to search nickname
	Keyword *string `form:"keyword,omitempty" json:"keyword,omitempty"`

	// ProductId Product ID to filter threads
	ProductId *string `form:"productId,omitempty" json:"productId,omitempty"`

	// Page The page number for pagination
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of threads per page
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetUserThreadsParamsStatus defines parameters for GetUserThreads.
type GetUserThreadsParamsStatus string

// GetUserThreadsMessagesParams defines parameters for GetUserThreadsMessages.
type GetUserThreadsMessagesParams struct {
	// Page The page number for pagination
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of threads per page
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetPurchasedItemsParams defines parameters for GetPurchasedItems.
type GetPurchasedItemsParams struct {
	TransactionStatus *GetPurchasedItemsParamsTransactionStatus `form:"transactionStatus,omitempty" json:"transactionStatus,omitempty"`
	From              *string                                   `form:"from,omitempty" json:"from,omitempty"`
	To                *string                                   `form:"to,omitempty" json:"to,omitempty"`
	OrderBy           *GetPurchasedItemsParamsOrderBy           `form:"orderBy,omitempty" json:"orderBy,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetPurchasedItemsParamsTransactionStatus defines parameters for GetPurchasedItems.
type GetPurchasedItemsParamsTransactionStatus string

// GetPurchasedItemsParamsOrderBy defines parameters for GetPurchasedItems.
type GetPurchasedItemsParamsOrderBy string

// GetFavoriteProductsParams defines parameters for GetFavoriteProducts.
type GetFavoriteProductsParams struct {
	// Page Page number
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetFavoriteSellersParams defines parameters for GetFavoriteSellers.
type GetFavoriteSellersParams struct {
	// Page Page number
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetUserNotificationsParams defines parameters for GetUserNotifications.
type GetUserNotificationsParams struct {
	// SystemType Filter notifications by type
	SystemType *GetUserNotificationsParamsSystemType `form:"systemType,omitempty" json:"systemType,omitempty"`

	// Page Page number
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of notifications per page
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetUserNotificationsParamsSystemType defines parameters for GetUserNotifications.
type GetUserNotificationsParamsSystemType string

// AuthJSONRequestBody defines body for Auth for application/json ContentType.
type AuthJSONRequestBody = AuthRequest

// ConfirmResetPasswordJSONRequestBody defines body for ConfirmResetPassword for application/json ContentType.
type ConfirmResetPasswordJSONRequestBody = ResetPasswordConfirmRequest

// RequestResetPasswordJSONRequestBody defines body for RequestResetPassword for application/json ContentType.
type RequestResetPasswordJSONRequestBody = ResetPasswordRequestRequest

// VerifyResetPasswordJSONRequestBody defines body for VerifyResetPassword for application/json ContentType.
type VerifyResetPasswordJSONRequestBody = ResetPasswordVerifyRequest

// SignupJSONRequestBody defines body for Signup for application/json ContentType.
type SignupJSONRequestBody = SignupRequest

// VerifySignupJSONRequestBody defines body for VerifySignup for application/json ContentType.
type VerifySignupJSONRequestBody = SignupVerifyRequest

// AddCartItemJSONRequestBody defines body for AddCartItem for application/json ContentType.
type AddCartItemJSONRequestBody = CartItem

// UpdateCartItemJSONRequestBody defines body for UpdateCartItem for application/json ContentType.
type UpdateCartItemJSONRequestBody = CartItem

// VerifyCreditCardJSONRequestBody defines body for VerifyCreditCard for application/json ContentType.
type VerifyCreditCardJSONRequestBody = VerifyCreditCardRequest

// CreateInquiryJSONRequestBody defines body for CreateInquiry for application/json ContentType.
type CreateInquiryJSONRequestBody = InquiryCreateRequest

// SendMessageJSONRequestBody defines body for SendMessage for application/json ContentType.
type SendMessageJSONRequestBody = SendMessageRequest

// CreateOrderJSONRequestBody defines body for CreateOrder for application/json ContentType.
type CreateOrderJSONRequestBody = CreateOrderRequest

// SearchProductsJSONRequestBody defines body for SearchProducts for application/json ContentType.
type SearchProductsJSONRequestBody = SearchProductsRequest

// CreateReportJSONRequestBody defines body for CreateReport for application/json ContentType.
type CreateReportJSONRequestBody = Report

// BlockUserJSONRequestBody defines body for BlockUser for application/json ContentType.
type BlockUserJSONRequestBody = BlockUserRequest

// AddSellerProductsJSONRequestBody defines body for AddSellerProducts for application/json ContentType.
type AddSellerProductsJSONRequestBody = ProductRequest

// UpdateProductByIdJSONRequestBody defines body for UpdateProductById for application/json ContentType.
type UpdateProductByIdJSONRequestBody = ProductUpdateRequest

// UpdatePublishStatusSellerJSONRequestBody defines body for UpdatePublishStatusSeller for application/json ContentType.
type UpdatePublishStatusSellerJSONRequestBody = UpdatePublishSellerRequest

// UpdateSoldItemStatusJSONRequestBody defines body for UpdateSoldItemStatus for application/json ContentType.
type UpdateSoldItemStatusJSONRequestBody = UpdateSoldItemStatusRequest

// CancelSubscriptionMembershipAndroidJSONRequestBody defines body for CancelSubscriptionMembershipAndroid for application/json ContentType.
type CancelSubscriptionMembershipAndroidJSONRequestBody = CancelSubscriptionAndroidRequest

// CancelSubscriptionMembershipIosJSONRequestBody defines body for CancelSubscriptionMembershipIos for application/json ContentType.
type CancelSubscriptionMembershipIosJSONRequestBody = CancelSubscriptionIOSRequest

// CreateThreadJSONRequestBody defines body for CreateThread for application/json ContentType.
type CreateThreadJSONRequestBody = CreateThreadRequest

// UploadFileMultipartRequestBody defines body for UploadFile for multipart/form-data ContentType.
type UploadFileMultipartRequestBody = UploadFileRequest

// CreateOrderDetailReviewJSONRequestBody defines body for CreateOrderDetailReview for application/json ContentType.
type CreateOrderDetailReviewJSONRequestBody = CreateReviewRequest

// SubscriptionMembershipJSONRequestBody defines body for SubscriptionMembership for application/json ContentType.
type SubscriptionMembershipJSONRequestBody = SubscriptionMembershipRequest

// RegisterUserJSONRequestBody defines body for RegisterUser for application/json ContentType.
type RegisterUserJSONRequestBody = RegisterUserRequest

// CancelAccountJSONRequestBody defines body for CancelAccount for application/json ContentType.
type CancelAccountJSONRequestBody = CancelAccountRequest

// UpdateEmailJSONRequestBody defines body for UpdateEmail for application/json ContentType.
type UpdateEmailJSONRequestBody = UpdateEmailRequest

// VerifyEmailJSONRequestBody defines body for VerifyEmail for application/json ContentType.
type VerifyEmailJSONRequestBody = VerifyEmailRequest

// DeleteFavoriteProductsJSONRequestBody defines body for DeleteFavoriteProducts for application/json ContentType.
type DeleteFavoriteProductsJSONRequestBody = DeleteFavoriteProductsRequest

// AddFavoriteProductJSONRequestBody defines body for AddFavoriteProduct for application/json ContentType.
type AddFavoriteProductJSONRequestBody = AddFavoriteProductRequest

// DeleteFavoriteSellersJSONRequestBody defines body for DeleteFavoriteSellers for application/json ContentType.
type DeleteFavoriteSellersJSONRequestBody = DeleteFavoriteSellersRequest

// AddFavoriteSellerJSONRequestBody defines body for AddFavoriteSeller for application/json ContentType.
type AddFavoriteSellerJSONRequestBody = AddFavoriteSellersRequest

// DeleteUserFcmTokenJSONRequestBody defines body for DeleteUserFcmToken for application/json ContentType.
type DeleteUserFcmTokenJSONRequestBody = DeleteFCMTokenRequest

// UpsertUserFcmTokenJSONRequestBody defines body for UpsertUserFcmToken for application/json ContentType.
type UpsertUserFcmTokenJSONRequestBody = UpsertFCMTokenRequest

// UpdateUserNotificationSettingsJSONRequestBody defines body for UpdateUserNotificationSettings for application/json ContentType.
type UpdateUserNotificationSettingsJSONRequestBody = NotificationSettingsRequest

// UpdatePasswordJSONRequestBody defines body for UpdatePassword for application/json ContentType.
type UpdatePasswordJSONRequestBody = UpdatePasswordRequest

// UpdateUserPaymentMethodJSONRequestBody defines body for UpdateUserPaymentMethod for application/json ContentType.
type UpdateUserPaymentMethodJSONRequestBody = UpdatePaymentMethodsRequest

// SettingPinCodeJSONRequestBody defines body for SettingPinCode for application/json ContentType.
type SettingPinCodeJSONRequestBody = SettingPinRequest

// UpdatePinCodeJSONRequestBody defines body for UpdatePinCode for application/json ContentType.
type UpdatePinCodeJSONRequestBody = UpdatePinRequest

// CheckPinCodeJSONRequestBody defines body for CheckPinCode for application/json ContentType.
type CheckPinCodeJSONRequestBody = CheckPinRequest

// UpdateUserProfileJSONRequestBody defines body for UpdateUserProfile for application/json ContentType.
type UpdateUserProfileJSONRequestBody = UpdateUserProfileRequest

// UpdateSellerInfoJSONRequestBody defines body for UpdateSellerInfo for application/json ContentType.
type UpdateSellerInfoJSONRequestBody = UpdateSellerRequest

// UpdateSellerBankAccountJSONRequestBody defines body for UpdateSellerBankAccount for application/json ContentType.
type UpdateSellerBankAccountJSONRequestBody = BankAccount

// UpdateConfirmTermsJSONRequestBody defines body for UpdateConfirmTerms for application/json ContentType.
type UpdateConfirmTermsJSONRequestBody = UpdateUserTermsRequest

// CreateVaultPaymentTokenJSONRequestBody defines body for CreateVaultPaymentToken for application/json ContentType.
type CreateVaultPaymentTokenJSONRequestBody = CreateVaultPaymentTokenRequest

// CreateVaultSetupTokenJSONRequestBody defines body for CreateVaultSetupToken for application/json ContentType.
type CreateVaultSetupTokenJSONRequestBody = CreateVaultSetupTokenRequest

// AsProduct returns the union data inside the Report_Item as a Product
func (t Report_Item) AsProduct() (Product, error) {
	var body Product
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromProduct overwrites any union data inside the Report_Item as the provided Product
func (t *Report_Item) FromProduct(v Product) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeProduct performs a merge with any union data inside the Report_Item, using the provided Product
func (t *Report_Item) MergeProduct(v Product) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsUser returns the union data inside the Report_Item as a User
func (t Report_Item) AsUser() (User, error) {
	var body User
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromUser overwrites any union data inside the Report_Item as the provided User
func (t *Report_Item) FromUser(v User) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeUser performs a merge with any union data inside the Report_Item, using the provided User
func (t *Report_Item) MergeUser(v User) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsSeller returns the union data inside the Report_Item as a Seller
func (t Report_Item) AsSeller() (Seller, error) {
	var body Seller
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromSeller overwrites any union data inside the Report_Item as the provided Seller
func (t *Report_Item) FromSeller(v Seller) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeSeller performs a merge with any union data inside the Report_Item, using the provided Seller
func (t *Report_Item) MergeSeller(v Seller) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t Report_Item) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *Report_Item) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsUser returns the union data inside the Report_Reporter as a User
func (t Report_Reporter) AsUser() (User, error) {
	var body User
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromUser overwrites any union data inside the Report_Reporter as the provided User
func (t *Report_Reporter) FromUser(v User) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeUser performs a merge with any union data inside the Report_Reporter, using the provided User
func (t *Report_Reporter) MergeUser(v User) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsSeller returns the union data inside the Report_Reporter as a Seller
func (t Report_Reporter) AsSeller() (Seller, error) {
	var body Seller
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromSeller overwrites any union data inside the Report_Reporter as the provided Seller
func (t *Report_Reporter) FromSeller(v Seller) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeSeller performs a merge with any union data inside the Report_Reporter, using the provided Seller
func (t *Report_Reporter) MergeSeller(v Seller) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t Report_Reporter) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *Report_Reporter) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

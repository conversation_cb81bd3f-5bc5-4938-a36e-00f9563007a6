package api

import (
	"strings"

	"as-api/as/api/admin"
	"as-api/as/api/user"
	"as-api/as/cache"
	"as-api/as/foundations/env"
	"as-api/as/foundations/logger"
	"as-api/as/foundations/middleware"

	"github.com/go-chi/chi/v5"
	"go.uber.org/dig"
)

type HTTPHandler struct {
	dig.In

	admin.HTTPHandlerAdmin
	user.HTTPHandlerUser

	CacheService cache.CacheService
	Logger       logger.Logger
}

var _skipURLs = []string{
	"POST /auth",
	"POST /auth/reset-password/request",
	"POST /auth/reset-password/confirm",
	"POST /auth/reset-password/verify",
	"GET /user/v1/faqs",
	"GET /user/v1/terms",
	"GET /user/v1/countries",
	"GET /user/v1/system/status",
	"GET /user/v1/brands",
	"GET /user/v1/product-categories",
	"POST /user/v1/products/search",
	"GET /user/v1/products/[^/]+$",
	"POST /user/v1/inquiries",
	"GET /user/v1/banners",
	"GET /user/v1/sellers/[^/]+$",
	"GET /user/v1/sellers/[^/]+/products$",
	"POST /user/v1/subscriptions/cancel/android",
	"POST /user/v1/subscriptions/cancel/ios",
	"GET /user/v1/home-feeds",
	"GET /user/v1/seller/dashboard",
}

func skipURLs(e middleware.Enforcer) middleware.Enforcer {
	for _, skipURL := range _skipURLs {
		s := strings.SplitN(skipURL, " ", 2)
		if len(s) < 2 {
			continue
		}

		e.AddSkipURL(s[0], s[1])
	}

	return e
}

// BuildRoute to create a sub-Router along a /api string.
func BuildRoute(h HTTPHandler, r chi.Router, env env.MapperData, logger logger.Logger) {
	// init enforcer
	enforcer, err := middleware.NewEnforcer(env, logger)
	if err != nil || enforcer == nil {
		panic(err)
	}

	// init maintenance checker
	maintenanceChecker := middleware.NewMaintenanceChecker(h.CacheService, h.Logger)

	// add business controllers to router
	r.Group(func(r chi.Router) {
		// add url to notify enforcer to skip it
		skipURLs(enforcer)

		// handers API
		r.Route("/user/v1", func(subr chi.Router) {
			// add maintenance middleware for User API only
			subr.Use(middleware.MaintenanceMiddleware(maintenanceChecker, h.Logger))

			// add middlewares
			subr.Use(enforcer.Authentication())

			// handler API
			h.HTTPHandlerUser.HandlerAPI(subr)
		})

		// handers API
		r.Route("/admin/v1", func(subr chi.Router) {
			// add middlewares (no maintenance middleware for admin)
			subr.Use(enforcer.AuthenticationAdmin())

			// handler API
			h.HTTPHandlerAdmin.HandlerAPI(subr)
		})
	})
}

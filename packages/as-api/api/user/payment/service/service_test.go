package service

import (
	"context"
	"errors"
	"testing"

	"as-api/as/api/user/payment/domain"
	dtos "as-api/as/dtos/user"
	"as-api/as/internal/auth"
	"as-api/as/internal/payment"
	"as-api/as/internal/paypal"
	"as-api/as/internal/user"
	authmock "as-api/as/mocks/domains/auth"
	mockdeeplink "as-api/as/mocks/domains/deeplink"
	paymentmock "as-api/as/mocks/domains/payment"
	mockpaypal "as-api/as/mocks/domains/paypal"
	usermock "as-api/as/mocks/domains/user"
	validatormock "as-api/as/mocks/validator"
	pkgcontext "as-api/as/pkg/context"
	"as-api/as/pkg/helpers/pointer"
	"as-api/as/pkg/jwt"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestVerifyCreditCard(t *testing.T) {
	testAuthID := "auth-123"
	testUserID := "user-123"
	testToken := "test-token"

	tests := []struct {
		name          string
		setupMock     func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context
		request       *dtos.VerifyCreditCardRequest
		expected      *dtos.VerifyCreditCardResponse
		expectedError string
	}{
		{
			name: "Success - Verify credit card without user",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   nil,
				}, nil)

				// Mock payment domain
				paymentDomain.On("VerifyCard", mock.Anything, mock.MatchedBy(func(req *payment.VerifyCardRequest) bool {
					return req.Token == testToken &&
						req.Payer.Email == "<EMAIL>"
				})).Return(&payment.VerifyCardResponse{
					AccessID:    pointer.Ptr("access-123"),
					RedirectURL: pointer.Ptr("https://redirect.url"),
				}, nil)

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected: &dtos.VerifyCreditCardResponse{
				AccessId:    pointer.Ptr("access-123"),
				RedirectUrl: pointer.Ptr("https://redirect.url"),
			},
			expectedError: "",
		},
		{
			name: "Success - Verify credit card with user and address",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   &testUserID,
				}, nil)

				// Mock user domain
				userDomain.On("ReadOne", mock.Anything, testUserID).Return(&user.User{
					ID:        pointer.Ptr(testUserID),
					Email:     "<EMAIL>",
					FirstName: pointer.Ptr("John"),
					LastName:  pointer.Ptr("Doe"),
					CreditCard: &user.CreditCard{
						CardID: pointer.Ptr("card-123"),
					},
					HomeAddress: &user.Address{},
				}, nil)

				// Mock payment domain
				paymentDomain.On("VerifyCard", mock.Anything, mock.MatchedBy(func(req *payment.VerifyCardRequest) bool {
					return req.Token == testToken &&
						req.Payer.Email == "<EMAIL>" &&
						req.Payer.Name == "John Doe"
				})).Return(&payment.VerifyCardResponse{
					AccessID:    pointer.Ptr("access-123"),
					RedirectURL: pointer.Ptr("https://redirect.url"),
				}, nil)

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
				Address: &dtos.Address{
					FullName: pointer.Ptr("Test User"),
					Phone: &dtos.Phone{
						CountryCode: "+1",
						Number:      "1234567890",
					},
					Mobile: &dtos.Phone{
						CountryCode: "+1",
						Number:      "0987654321",
					},
				},
			},
			expected: &dtos.VerifyCreditCardResponse{
				AccessId:    pointer.Ptr("access-123"),
				RedirectUrl: pointer.Ptr("https://redirect.url"),
			},
			expectedError: "",
		},
		{
			name: "Error - Validation fails",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Mock validator returning error
				validator.On("Validate", mock.Anything).Return(errors.New("validation error"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "validate request: validation error",
		},
		{
			name: "Error - Failed to get auth ID from context",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// No claims in context
				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "get auth id:",
		},
		{
			name: "Error - Failed to find auth",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain returning error
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(nil, errors.New("auth not found"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "get auth: auth not found",
		},
		{
			name: "Error - Failed to get user",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   &testUserID,
				}, nil)

				// Mock user domain returning error
				userDomain.On("ReadOne", mock.Anything, testUserID).Return(nil, errors.New("user not found"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "get user: user not found",
		},
		{
			name: "Error - User not found",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   &testUserID,
				}, nil)

				// Mock user domain returning nil user
				userDomain.On("ReadOne", mock.Anything, testUserID).Return(nil, nil)

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "user not found",
		},
		{
			name: "Error - Payment verification fails",
			setupMock: func(paymentDomain *paymentmock.PaymentDomain, userDomain *usermock.UserDomain, authDomain *authmock.AuthDomain, validator *validatormock.Validator, ctx context.Context) context.Context {
				// Set claims in context
				claims := jwt.CustomClaims{
					AID: testAuthID,
				}
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

				// Mock validator
				validator.On("Validate", mock.Anything).Return(nil)

				// Mock auth domain
				authDomain.On("FindOneByID", mock.Anything, testAuthID).Return(&auth.Auth{
					ID:       testAuthID,
					Username: "<EMAIL>",
					UserID:   nil,
				}, nil)

				// Mock payment domain returning error
				paymentDomain.On("VerifyCard", mock.Anything, mock.Anything).Return(nil, errors.New("payment verification failed"))

				return ctx
			},
			request: &dtos.VerifyCreditCardRequest{
				Token: testToken,
			},
			expected:      nil,
			expectedError: "verify card: payment verification failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			mockPaymentDomain := paymentmock.NewPaymentDomain(t)
			mockUserDomain := usermock.NewUserDomain(t)
			mockAuthDomain := authmock.NewAuthDomain(t)
			mockValidator := validatormock.NewValidator(t)

			// Setup mocks
			ctx := tt.setupMock(mockPaymentDomain, mockUserDomain, mockAuthDomain, mockValidator, context.Background())

			// Create mock domains
			mockDeepLinkDomain := mockdeeplink.NewDeepLinkDomain(t)
			mockPayPalDomain := mockpaypal.NewPayPalDomain(t)

			// Create service
			service := NewPaymentService(mockPaymentDomain, mockUserDomain, mockAuthDomain, mockDeepLinkDomain, mockPayPalDomain, mockValidator)

			// Call the service
			result, err := service.VerifyCreditCard(ctx, tt.request)

			// Check error
			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
			}

			// Check result
			if tt.expected == nil {
				assert.Nil(t, result)
			} else {
				assert.NotNil(t, result)
				assert.Equal(t, pointer.Safe(tt.expected.AccessId), pointer.Safe(result.AccessId))
				assert.Equal(t, pointer.Safe(tt.expected.RedirectUrl), pointer.Safe(result.RedirectUrl))
			}
		})
	}
}

func TestNewPaymentService(t *testing.T) {
	mockPaymentDomain := paymentmock.NewPaymentDomain(t)
	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockValidator := validatormock.NewValidator(t)

	// Create mock domains
	mockDeepLinkDomain := mockdeeplink.NewDeepLinkDomain(t)
	mockPayPalDomain := mockpaypal.NewPayPalDomain(t)

	service := NewPaymentService(mockPaymentDomain, mockUserDomain, mockAuthDomain, mockDeepLinkDomain, mockPayPalDomain, mockValidator)

	assert.NotNil(t, service)
	assert.Implements(t, (*domain.PaymentService)(nil), service)
}

func TestGetVaultTokens(t *testing.T) {
	t.Parallel()

	mockValidator := new(validatormock.Validator)
	mockPaymentDomain := paymentmock.NewPaymentDomain(t)
	mockUserDomain := usermock.NewUserDomain(t)
	mockAuthDomain := authmock.NewAuthDomain(t)
	mockDeepLinkDomain := mockdeeplink.NewDeepLinkDomain(t)
	mockPayPalDomain := mockpaypal.NewPayPalDomain(t)
	svc := NewPaymentService(mockPaymentDomain, mockUserDomain, mockAuthDomain, mockDeepLinkDomain, mockPayPalDomain, mockValidator)
	ctx := context.Background()

	// Set customer ID in context via claims
	claims := jwt.CustomClaims{
		CID: "customer123",
	}
	ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, claims)

	tests := []struct {
		name    string
		mock    func()
		want    *dtos.GetVaultTokensResponse
		wantErr error
	}{
		{
			name: "SUCCESS - Get vault tokens",
			mock: func() {
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockPayPalDomain.On("GetVaultTokenByCustomerID", ctx, "customer123").Return(&paypal.VaultTokenData{
					CustomerID:  "customer123",
					VaultCardID: "vault-card-456",
					CardInfo: &paypal.CardInfo{
						Brand:      pointer.Ptr("Visa"),
						LastDigits: pointer.Ptr("1234"),
						Name:       pointer.Ptr("John Doe"),
						Expiry:     pointer.Ptr("2025-12"),
					},
				}, nil).Once()
			},
			want: &dtos.GetVaultTokensResponse{
				VaultToken: &dtos.VaultToken{
					CardInfo: dtos.CreditCard{
						Brand:          pointer.Ptr("Visa"),
						CardNumber:     pointer.Ptr("************1234"),
						CardHolderName: pointer.Ptr("John Doe"),
						ExpiryMonth:    pointer.Ptr("12"),
						ExpiryYear:     pointer.Ptr("2025"),
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "FAILED - No vault tokens found",
			mock: func() {
				mockValidator.On("Validate", mock.Anything).Return(nil).Once()
				mockPayPalDomain.On("GetVaultTokenByCustomerID", ctx, "customer123").Return((*paypal.VaultTokenData)(nil), nil).Once()
			},
			want:    nil,
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			tt.mock()

			got, err := svc.GetVaultTokens(ctx)
			if tt.wantErr != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.wantErr.Error())
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

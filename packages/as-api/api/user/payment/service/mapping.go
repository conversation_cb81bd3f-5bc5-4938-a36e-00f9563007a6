package service

import (
	"fmt"

	dtos "as-api/as/dtos/user"
	"as-api/as/internal/payment"
	"as-api/as/internal/paypal"
	"as-api/as/internal/user"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

func getDomainPayerFromDomainUser(user *user.User) (*payment.Payer, error) {
	if user == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "user not found")
	}

	payer := &payment.Payer{
		Email:     user.Email,
		Name:      fmt.Sprintf("%s %s", pointer.Safe(user.FirstName), pointer.Safe(user.LastName)),
		AccountID: pointer.Safe(user.ID),
	}

	address := user.BillingAddress
	if address == nil || !address.IsDifferentFromResidence {
		address = user.HomeAddress
	}

	if address == nil {
		return nil, errors.Wrap(apiutil.ErrResourceNotFound, "address not found")
	}

	if name := pointer.Safe(address.FullName); name != "" {
		payer.Name = name
	}

	payer.HomePhone = pointer.Safe(convertDomainUserPhoneToDomainPaymentPhone(address.Phone))
	payer.MobilePhone = convertDomainUserPhoneToDomainPaymentPhone(address.Mobile)

	return payer, nil
}

func convertDTOPhoneToDomainPaymentPhone(phone *dtos.Phone) *payment.Phone {
	if phone == nil {
		return nil
	}

	return &payment.Phone{
		CountryCode: phone.CountryCode,
		Number:      phone.Number,
	}
}

func convertDomainUserPhoneToDomainPaymentPhone(phone *user.Phone) *payment.Phone {
	if phone == nil {
		return nil
	}

	return &payment.Phone{
		CountryCode: phone.CountryCode,
		Number:      phone.Number,
	}
}

func convertToPayPalBillingAddress(address *dtos.Address) *paypal.AddressPortable {
	if address == nil {
		return nil
	}

	return &paypal.AddressPortable{
		AddressLine1: address.Address1,
		AddressLine2: address.Address2,
		AdminArea2:   address.City,
		AdminArea1:   address.State,
		PostalCode:   address.PostalCode,
		CountryCode:  pointer.Safe(address.Country),
	}
}

func maskCardNumber(cardNumber string) string {
	cardNumber = "****" + cardNumber
	return fmt.Sprintf("************%s", cardNumber[len(cardNumber)-4:])
}

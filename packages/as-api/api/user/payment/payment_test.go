package payment

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/logger"
	mockpayment "as-api/as/mocks/services/user/payment"
	"as-api/as/pkg/helpers/pointer"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestVerifyCreditCard(t *testing.T) {
	t.Skip("TODO: Fix this test - VerifyCreditCard is not part of the generated ServerInterface")
	tests := []struct {
		name           string
		requestBody    interface{}
		setupMock      func(mockService *mockpayment.PaymentService)
		expectedStatus int
		expectedBody   interface{}
	}{
		{
			name: "Success - Credit card verification successful",
			requestBody: &dtos.VerifyCreditCardRequest{
				Token: "test-token-123",
			},
			setupMock: func(mockService *mockpayment.PaymentService) {
				mockService.On("VerifyCreditCard", mock.Anything, mock.MatchedBy(func(req *dtos.VerifyCreditCardRequest) bool {
					return req.Token == "test-token-123"
				})).Return(&dtos.VerifyCreditCardResponse{
					AccessId:    pointer.Ptr("access-123"),
					RedirectUrl: pointer.Ptr("https://redirect.example.com"),
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody: &dtos.VerifyCreditCardResponse{
				AccessId:    pointer.Ptr("access-123"),
				RedirectUrl: pointer.Ptr("https://redirect.example.com"),
			},
		},
		{
			name: "Success - Credit card verification with address",
			requestBody: &dtos.VerifyCreditCardRequest{
				Token: "test-token-456",
				Address: &dtos.Address{
					FullName: pointer.Ptr("John Doe"),
					Phone: &dtos.Phone{
						CountryCode: "+1",
						Number:      "1234567890",
					},
					Mobile: &dtos.Phone{
						CountryCode: "+1",
						Number:      "0987654321",
					},
				},
			},
			setupMock: func(mockService *mockpayment.PaymentService) {
				mockService.On("VerifyCreditCard", mock.Anything, mock.MatchedBy(func(req *dtos.VerifyCreditCardRequest) bool {
					return req.Token == "test-token-456" &&
						req.Address != nil &&
						pointer.Safe(req.Address.FullName) == "John Doe"
				})).Return(&dtos.VerifyCreditCardResponse{
					AccessId:    pointer.Ptr("access-456"),
					RedirectUrl: pointer.Ptr("https://redirect-with-address.example.com"),
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody: &dtos.VerifyCreditCardResponse{
				AccessId:    pointer.Ptr("access-456"),
				RedirectUrl: pointer.Ptr("https://redirect-with-address.example.com"),
			},
		},
		{
			name: "Success - Credit card verification with only access ID",
			requestBody: &dtos.VerifyCreditCardRequest{
				Token: "test-token-789",
			},
			setupMock: func(mockService *mockpayment.PaymentService) {
				mockService.On("VerifyCreditCard", mock.Anything, mock.MatchedBy(func(req *dtos.VerifyCreditCardRequest) bool {
					return req.Token == "test-token-789"
				})).Return(&dtos.VerifyCreditCardResponse{
					AccessId:    pointer.Ptr("access-789"),
					RedirectUrl: nil,
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody: &dtos.VerifyCreditCardResponse{
				AccessId:    pointer.Ptr("access-789"),
				RedirectUrl: nil,
			},
		},
		{
			name:        "Error - Invalid JSON request body",
			requestBody: "invalid json",
			setupMock: func(mockService *mockpayment.PaymentService) {
				// No service call expected
			},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   nil,
		},
		{
			name: "Error - Empty request body",
			requestBody: &dtos.VerifyCreditCardRequest{
				Token: "",
			},
			setupMock: func(mockService *mockpayment.PaymentService) {
				mockService.On("VerifyCreditCard", mock.Anything, mock.Anything).
					Return(nil, errors.New("validation error: token is required"))
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
		},
		{
			name: "Error - Service returns validation error",
			requestBody: &dtos.VerifyCreditCardRequest{
				Token: "invalid-token",
			},
			setupMock: func(mockService *mockpayment.PaymentService) {
				mockService.On("VerifyCreditCard", mock.Anything, mock.Anything).
					Return(nil, errors.New("invalid request: token format is invalid"))
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
		},
		{
			name: "Error - Service returns authentication error",
			requestBody: &dtos.VerifyCreditCardRequest{
				Token: "test-token",
			},
			setupMock: func(mockService *mockpayment.PaymentService) {
				mockService.On("VerifyCreditCard", mock.Anything, mock.Anything).
					Return(nil, errors.New("permission denied: invalid auth context"))
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
		},
		{
			name: "Error - Service returns payment gateway error",
			requestBody: &dtos.VerifyCreditCardRequest{
				Token: "test-token",
			},
			setupMock: func(mockService *mockpayment.PaymentService) {
				mockService.On("VerifyCreditCard", mock.Anything, mock.Anything).
					Return(nil, errors.New("verify card: payment gateway error"))
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock service
			mockService := mockpayment.NewPaymentService(t)
			tt.setupMock(mockService)

			// Create the API handler
			// TODO: Fix this test - VerifyCreditCard is not part of the generated ServerInterface
			// apiHandler := NewAPI(mockService, logger.NewAPILogger())

			// Prepare request body
			var requestBody []byte
			var err error

			if tt.requestBody == "invalid json" {
				requestBody = []byte("invalid json")
			} else {
				requestBody, err = json.Marshal(tt.requestBody)
				require.NoError(t, err)
			}

			// Create test request
			req, err := http.NewRequest("POST", "/credit-cards", bytes.NewBuffer(requestBody))
			require.NoError(t, err)
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call the handler
			// TODO: Fix this test - VerifyCreditCard is not part of the generated ServerInterface
			// apiHandler.VerifyCreditCard(rr, req)

			// Check status code
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// Check response body for successful responses
			if tt.expectedStatus == http.StatusOK && tt.expectedBody != nil {
				expectedJSON, err := json.Marshal(tt.expectedBody)
				require.NoError(t, err)

				assert.JSONEq(t, string(expectedJSON), rr.Body.String())
			}

			// Verify that response has proper content type for success responses
			if tt.expectedStatus == http.StatusOK {
				assert.Contains(t, rr.Header().Get("Content-Type"), "application/json")
			}

			// Assert mock expectations
			mockService.AssertExpectations(t)
		})
	}
}

func TestNewAPI(t *testing.T) {
	mockService := mockpayment.NewPaymentService(t)
	logger := logger.NewAPILogger()

	api := NewAPI(mockService, logger)

	assert.NotNil(t, api)
	assert.Implements(t, (*ServerInterface)(nil), api)
}

func TestNewAPI_HandlerSetup(t *testing.T) {
	// Test that the API can be initialized without panicking
	mockService := mockpayment.NewPaymentService(t)
	logger := logger.NewAPILogger()

	assert.NotPanics(t, func() {
		NewAPI(mockService, logger)
	})
}

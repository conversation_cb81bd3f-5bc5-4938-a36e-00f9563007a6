package payment

import (
	"log"
	"net/http"

	"as-api/as/api/user/payment/domain"
	"as-api/as/api/user/payment/service"
	dtos "as-api/as/dtos/user"
	"as-api/as/foundations/logger"
	_ "as-api/as/internal/paypal" // Import to trigger init function
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/apiutil"

	"github.com/go-chi/chi/v5"
)

func init() {
	if err := di.RegisterProviders(NewAPI); err != nil {
		log.Fatal("register constructor payment api failed:", err)
	}

	if err := di.RegisterProviders(service.NewPaymentService); err != nil {
		log.Fatal("register constructor payment service failed:", err)
	}
}

type httpAPI struct {
	svc    domain.PaymentService
	logger logger.Logger
}

func NewAPI(svc domain.PaymentService, logger logger.Logger) ServerInterface {
	return &httpAPI{svc: svc, logger: logger}
}

func HandlerAPI(si ServerInterface, r chi.Router, log logger.Logger) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter:       r,
		ErrorHandlerFunc: apiutil.ErrorHandlerFunc(log),
	})
}

// Verify Credit Card
// (POST /credit-cards)
func (h *httpAPI) VerifyCreditCard(w http.ResponseWriter, r *http.Request) {
	var req *dtos.VerifyCreditCardRequest
	response := apiutil.NewJSONResponse(w, r, h.logger)

	if err := response.ProcessJSONRequestBody(&req); err != nil {
		response.Failure(err)
		return
	}

	res, err := h.svc.VerifyCreditCard(r.Context(), req)
	if err != nil {
		response.Failure(err)
		return
	}

	if err := response.Success(http.StatusOK, res); err != nil {
		h.logger.Error(err.Error())
	}
}

// Create Vault Setup Token
// (POST /vault/setup-token)
func (h *httpAPI) CreateVaultSetupToken(w http.ResponseWriter, r *http.Request) {
	var req dtos.CreateVaultSetupTokenRequest
	response := apiutil.NewJSONResponse(w, r, h.logger)

	if r.Body != nil && r.Body != http.NoBody {
		if err := response.ProcessJSONRequestBody(&req); err != nil {
			response.Failure(err)
			return
		}
	}

	res, err := h.svc.CreateVaultSetupToken(r.Context(), &req)
	if err != nil {
		response.Failure(err)
		return
	}

	if err := response.Success(http.StatusOK, res); err != nil {
		h.logger.Error(err.Error())
	}
}

// Create Vault Payment Token
// (POST /vault/payment-token)
func (h *httpAPI) CreateVaultPaymentToken(w http.ResponseWriter, r *http.Request) {
	var req *dtos.CreateVaultPaymentTokenRequest
	response := apiutil.NewJSONResponse(w, r, h.logger)

	if err := response.ProcessJSONRequestBody(&req); err != nil {
		response.Failure(err)
		return
	}

	res, err := h.svc.CreateVaultPaymentToken(r.Context(), req)
	if err != nil {
		response.Failure(err)
		return
	}

	if err := response.Success(http.StatusOK, res); err != nil {
		h.logger.Error(err.Error())
	}
}

// Get Vault Tokens
// (GET /vault/tokens)
func (h *httpAPI) GetVaultTokens(w http.ResponseWriter, r *http.Request) {
	response := apiutil.NewJSONResponse(w, r, h.logger)

	res, err := h.svc.GetVaultTokens(r.Context())
	if err != nil {
		response.Failure(err)
		return
	}

	if err := response.Success(http.StatusOK, res); err != nil {
		h.logger.Error(err.Error())
	}
}

package service

import (
	"fmt"
	"strings"
	"time"

	dtos "as-api/as/dtos/user"
	"as-api/as/internal/payment"
	"as-api/as/internal/paypal"
	"as-api/as/internal/user"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

func convertDTOBankAccountToDomain(req *dtos.BankAccount) *user.BankAccount {
	if req == nil {
		return nil
	}

	return &user.BankAccount{
		AccountHolderKatakana: req.AccountHolderKatakana,
		AccountHolderName:     req.AccountHolderName,
		AccountNumber:         req.AccountNumber,
		AccountType:           string(req.AccountType),
		BankCode:              req.BankCode,
		BankName:              req.BankName,
		BranchCode:            req.BranchCode,
		BranchName:            req.BranchName,
		Country:               req.Country,
		Iban:                  req.Iban,
		Pin:                   req.Pin,
		RoutingNumber:         req.Routing<PERSON>umber,
		SwiftCode:             req.SwiftCode,
	}
}

// Helper function to convert domain bank account to DTO bank account
func convertDomainBankAccountToDTO(domainBank *user.BankAccount) *dtos.BankAccount {
	if domainBank == nil {
		return nil
	}

	return &dtos.BankAccount{
		AccountHolderKatakana: domainBank.AccountHolderKatakana,
		AccountHolderName:     domainBank.AccountHolderName,
		AccountNumber:         domainBank.AccountNumber,
		AccountType:           dtos.BankAccountAccountType(domainBank.AccountType),
		BankCode:              domainBank.BankCode,
		BankName:              domainBank.BankName,
		BranchCode:            domainBank.BranchCode,
		BranchName:            domainBank.BranchName,
		Country:               domainBank.Country,
		Iban:                  domainBank.Iban,
		Pin:                   domainBank.Pin,
		RoutingNumber:         domainBank.RoutingNumber,
		SwiftCode:             domainBank.SwiftCode,
	}
}

// Helper function to convert domain address to DTO address
func convertDomainAddressToDTO(domainAddr *user.Address) *dtos.Address {
	if domainAddr == nil {
		return nil
	}

	return &dtos.Address{
		IsDifferentFromResidence: pointer.Ptr(domainAddr.IsDifferentFromResidence),
		Address1:                 domainAddr.Address1,
		Address2:                 domainAddr.Address2,
		City:                     domainAddr.City,
		Country:                  domainAddr.Country,
		FirstName:                domainAddr.FirstName,
		FullName:                 domainAddr.FullName,
		LastName:                 domainAddr.LastName,
		Mobile:                   convertDomainPhoneToDTO(domainAddr.Mobile),
		Phone:                    convertDomainPhoneToDTO(domainAddr.Phone),
		PostalCode:               domainAddr.PostalCode,
		State:                    domainAddr.State,
		RegionId:                 domainAddr.RegionId,
	}
}

func convertDTOPhoneToDomain(dtoPhone *dtos.Phone) *user.Phone {
	if dtoPhone == nil {
		return nil
	}

	return &user.Phone{
		CountryCode: dtoPhone.CountryCode,
		Number:      dtoPhone.Number,
	}
}

func convertDomainPhoneToDTO(domainPhone *user.Phone) *dtos.Phone {
	if domainPhone == nil {
		return nil
	}

	return &dtos.Phone{
		CountryCode: domainPhone.CountryCode,
		Number:      domainPhone.Number,
	}
}

// Helper function to update user information from request
func updateUserFromRequest(existingUser *user.User, req dtos.UpdateUserProfileRequest) error {
	// Update user fields
	if req.User != nil {
		if req.User.FirstName != nil {
			existingUser.FirstName = req.User.FirstName
		}
		if req.User.LastName != nil {
			existingUser.LastName = req.User.LastName
		}
		if req.User.Nickname != nil {
			existingUser.Nickname = req.User.Nickname
		}
		if req.User.Gender != nil {
			existingUser.Gender = req.User.Gender
		}
		if req.User.DateOfBirth != nil {
			dateOfBirth, err := time.Parse(time.DateOnly, *req.User.DateOfBirth)
			if err != nil {
				return errors.Wrap(err, "parse date of birth")
			}
			existingUser.DateOfBirth = &dateOfBirth
		}
		if req.User.Lang != nil {
			existingUser.Language = req.User.Lang
		}
		if req.User.CountryCode != nil {
			existingUser.CountryCode = req.User.CountryCode
		}
		if req.User.RegionId != nil {
			existingUser.RegionID = req.User.RegionId
		}
	}

	// Update addresses
	if req.HomeAddress != nil {
		existingUser.HomeAddress = convertDTOAddressToDomain(req.HomeAddress)
		existingUser.CountryCode = req.HomeAddress.Country
		existingUser.RegionID = req.HomeAddress.RegionId
	}
	if req.ShippingAddress != nil {
		existingUser.ShippingAddress = convertDTOAddressToDomain(req.ShippingAddress)
	}
	if req.BillingAddress != nil {
		existingUser.BillingAddress = convertDTOAddressToDomain(req.BillingAddress)
	}

	return nil
}

// Helper function to convert DTO address to domain address
func convertDTOAddressToDomain(dtoAddr *dtos.Address) *user.Address {
	if dtoAddr == nil {
		return nil
	}

	return &user.Address{
		IsDifferentFromResidence: pointer.Safe(dtoAddr.IsDifferentFromResidence),
		Address1:                 dtoAddr.Address1,
		Address2:                 dtoAddr.Address2,
		City:                     dtoAddr.City,
		Country:                  dtoAddr.Country,
		FirstName:                dtoAddr.FirstName,
		FullName:                 dtoAddr.FullName,
		LastName:                 dtoAddr.LastName,
		Mobile:                   convertDTOPhoneToDomain(dtoAddr.Mobile),
		Phone:                    convertDTOPhoneToDomain(dtoAddr.Phone),
		PostalCode:               dtoAddr.PostalCode,
		State:                    dtoAddr.State,
		RegionId:                 dtoAddr.RegionId,
	}
}

// Helper function to convert DTO register user request to domain user
func convertDTORegisterUserRequestToDomain(dtoUser *dtos.RegisterUserRequest) (*user.User, error) {
	u := &user.User{
		Email:             *dtoUser.Email,
		FirstName:         dtoUser.FirstName,
		LastName:          dtoUser.LastName,
		Nickname:          dtoUser.Nickname,
		Gender:            dtoUser.Gender,
		HomeAddress:       convertDTOAddressToDomain(dtoUser.HomeAddress),
		ShippingAddress:   convertDTOAddressToDomain(dtoUser.ShippingAddress),
		BillingAddress:    convertDTOAddressToDomain(dtoUser.BillingAddress),
		CountryCode:       dtoUser.CountryCode,
		RegionID:          dtoUser.RegionId,
		ReceiveNewsletter: dtoUser.ReceiveNewsletter,
		Language:          dtoUser.Lang,
	}

	if dtoUser.DateOfBirth != nil {
		dateOfBirth, err := time.Parse(time.DateOnly, *dtoUser.DateOfBirth)
		if err != nil {
			return nil, errors.Wrap(apiutil.ErrInvalidRequest, "parse date of birth")
		}
		u.DateOfBirth = &dateOfBirth
	}

	return u, nil
}

func convertDomainUserToDTO(domainUser *user.User) *dtos.User {
	userDTO := &dtos.User{
		Id:                domainUser.ID,
		AccountId:         domainUser.AccountId,
		Email:             &domainUser.Email,
		FirstName:         domainUser.FirstName,
		LastName:          domainUser.LastName,
		Nickname:          domainUser.Nickname,
		Gender:            domainUser.Gender,
		SellerId:          domainUser.SellerID,
		PinSetting:        &domainUser.PinSetting,
		CountryCode:       domainUser.CountryCode,
		RegionId:          domainUser.RegionID,
		ReceiveNewsletter: domainUser.ReceiveNewsletter,
		IsTermsRequired:   domainUser.IsTermsRequired,
		Membership:        domainUser.Membership,
		Lang:              domainUser.Language,
	}

	// Convert DateOfBirth from time.Time to string
	if domainUser.DateOfBirth != nil {
		userDTO.DateOfBirth = pointer.Ptr(domainUser.DateOfBirth.Format(time.DateOnly))
	}

	if domainUser.ConfirmTermDate != nil {
		userDTO.ConfirmTermDate = pointer.Ptr(domainUser.ConfirmTermDate.Format(time.RFC3339))
	}

	return userDTO
}

func convertDomainSellerToDTO(domainSeller *user.Seller) *dtos.Seller {
	return &dtos.Seller{
		Id:             domainSeller.ID,
		AccountId:      domainSeller.AccountID,
		AccountType:    dtos.SellerAccountType(domainSeller.AccountType),
		ShopName:       domainSeller.ShopName,
		About:          domainSeller.About,
		AvatarUrl:      domainSeller.AvatarUrl,
		FavoriteBrands: domainSeller.FavoriteBrands,
		HeaderImgUrl:   domainSeller.HeaderImgUrl,
		Specialty:      domainSeller.Specialty,
		StockLocation:  domainSeller.StockLocation,
		CountryCode:    domainSeller.CountryCode,
		RegionId:       domainSeller.RegionID,
	}
}

// ConvertDTOToEmailNotificationSettings converts DTO email notification settings to domain model
func ConvertDTOToEmailNotificationSettings(dto *dtos.EmailNotificationSettings) *user.EmailNotificationSettings {
	if dto == nil {
		return nil
	}

	return &user.EmailNotificationSettings{
		CampaignsAndPromotions: dto.CampaignsAndPromotions,
		CartAndFavorites:       dto.CartAndFavorites,
		FavoritedShops:         dto.FavoritedShops,
		ImportantNotices:       dto.ImportantNotices,
		ListedItems:            dto.ListedItems,
		Login:                  dto.Login,
		Messages:               dto.Messages,
		OrderStatus:            dto.OrderStatus,
		Withdrawals:            dto.Withdrawals,
	}
}

// ConvertEmailNotificationSettingsToDTO converts domain model email notification settings to DTO
func ConvertEmailNotificationSettingsToDTO(domainModel *user.EmailNotificationSettings) *dtos.EmailNotificationSettings {
	if domainModel == nil {
		return nil
	}

	return &dtos.EmailNotificationSettings{
		CampaignsAndPromotions: domainModel.CampaignsAndPromotions,
		CartAndFavorites:       domainModel.CartAndFavorites,
		FavoritedShops:         domainModel.FavoritedShops,
		ImportantNotices:       domainModel.ImportantNotices,
		ListedItems:            domainModel.ListedItems,
		Login:                  domainModel.Login,
		Messages:               domainModel.Messages,
		OrderStatus:            domainModel.OrderStatus,
		Withdrawals:            domainModel.Withdrawals,
	}
}

// ConvertDTOToPushNotificationSettings converts DTO push notification settings to domain model
func ConvertDTOToPushNotificationSettings(dto *dtos.PushNotificationSettings) *user.PushNotificationSettings {
	if dto == nil {
		return nil
	}

	return &user.PushNotificationSettings{
		CampaignsAndPromotions: dto.CampaignsAndPromotions,
		CartAndFavorites:       dto.CartAndFavorites,
		FavoritedShops:         dto.FavoritedShops,
		ImportantNotices:       dto.ImportantNotices,
		ListedItems:            dto.ListedItems,
		Messages:               dto.Messages,
		OrderStatus:            dto.OrderStatus,
		StopAll:                dto.StopAll,
		Withdrawals:            dto.Withdrawals,
	}
}

// ConvertPushNotificationSettingsToDTO converts domain model push notification settings to DTO
func ConvertPushNotificationSettingsToDTO(domainModel *user.PushNotificationSettings) *dtos.PushNotificationSettings {
	if domainModel == nil {
		return nil
	}

	return &dtos.PushNotificationSettings{
		CampaignsAndPromotions: domainModel.CampaignsAndPromotions,
		CartAndFavorites:       domainModel.CartAndFavorites,
		FavoritedShops:         domainModel.FavoritedShops,
		ImportantNotices:       domainModel.ImportantNotices,
		ListedItems:            domainModel.ListedItems,
		Messages:               domainModel.Messages,
		OrderStatus:            domainModel.OrderStatus,
		StopAll:                domainModel.StopAll,
		Withdrawals:            domainModel.Withdrawals,
	}
}

// ConvertDTOToNotificationSettings converts DTO notification settings to domain model
func ConvertDTOToNotificationSettings(dto dtos.NotificationSettingsRequest) *user.NotificationSettings {
	return &user.NotificationSettings{
		EmailNotifications: ConvertDTOToEmailNotificationSettings(dto.EmailNotifications),
		PushNotifications:  ConvertDTOToPushNotificationSettings(dto.PushNotifications),
	}
}

// ConvertNotificationSettingsToDTO converts domain model notification settings to DTO
func ConvertNotificationSettingsToDTO(domainModel *user.NotificationSettings) *dtos.NotificationSettingsResponse {
	// Handle nil case
	if domainModel == nil {
		return &dtos.NotificationSettingsResponse{}
	}

	return &dtos.NotificationSettingsResponse{
		EmailNotifications: ConvertEmailNotificationSettingsToDTO(domainModel.EmailNotifications),
		PushNotifications:  ConvertPushNotificationSettingsToDTO(domainModel.PushNotifications),
	}
}

func ConvertDomainCreditCardToDTO(domainCreditCard *user.CreditCard) *dtos.CreditCard {
	if domainCreditCard == nil {
		return nil
	}

	return &dtos.CreditCard{
		CardHolderName: domainCreditCard.CardHolderName,
		CardNumber:     domainCreditCard.CardNumber,
		ExpiryMonth:    domainCreditCard.ExpiryMonth,
		ExpiryYear:     domainCreditCard.ExpiryYear,
		Brand:          pointer.PtrString[string](domainCreditCard.Brand),
	}
}

func ConvertDomainPaymentCreditCardToDomainUser(storeCardRes *payment.StoreCardResponse) *user.CreditCard {
	if storeCardRes == nil || storeCardRes.CardResult == nil {
		return nil
	}

	return &user.CreditCard{
		CardID:         storeCardRes.CardID,
		CardHolderName: storeCardRes.CardResult.CardholderName,
		CardNumber:     storeCardRes.CardResult.CardNumber,
		ExpiryMonth:    storeCardRes.CardResult.ExpiryMonth,
		ExpiryYear:     storeCardRes.CardResult.ExpiryYear,
		Brand:          pointer.PtrString[string](storeCardRes.CardResult.Brand),
	}
}

func ConvertDomainPaypalVaultTokenToDomainUser(paypalToken *paypal.VaultTokenData) *user.CreditCard {
	if paypalToken == nil {
		return nil
	}

	card := &user.CreditCard{
		CardID: pointer.Ptr(paypalToken.VaultCardID),
	}

	if paypalToken.CardInfo != nil {
		card.CardHolderName = paypalToken.CardInfo.Name
		card.CardNumber = pointer.Ptr(maskCardNumber(pointer.Safe(paypalToken.CardInfo.LastDigits)))
		if expiry := strings.Split(pointer.Safe(paypalToken.CardInfo.Expiry), "-"); len(expiry) == 2 {
			card.ExpiryMonth = pointer.Ptr(expiry[1])
			card.ExpiryYear = pointer.Ptr(expiry[0])
		}
		card.Brand = pointer.PtrString[string](paypalToken.CardInfo.Brand)
	}

	return card
}

func maskCardNumber(cardNumber string) string {
	cardNumber = "****" + cardNumber
	return fmt.Sprintf("************%s", cardNumber[len(cardNumber)-4:])
}

// Helper function to convert domain company info to DTO company info
func convertDomainCompanyInfoToDTO(domainCompany *user.CompanyInfo) *dtos.CompanyInfo {
	if domainCompany == nil {
		return nil
	}

	return &dtos.CompanyInfo{
		CompanyName: domainCompany.CompanyName,
		ContactName: domainCompany.ContactName,
		DunsNumber:  domainCompany.DunsNumber,
		Email:       domainCompany.Email,
		PhoneNumber: domainCompany.PhoneNumber,
		WebsiteURL:  domainCompany.WebsiteURL,
	}
}

// Helper function to convert DTO company info to domain company info
func convertDTOCompanyInfoToDomain(dtoCompany *dtos.CompanyInfo) *user.CompanyInfo {
	if dtoCompany == nil {
		return nil
	}

	return &user.CompanyInfo{
		CompanyName: dtoCompany.CompanyName,
		ContactName: dtoCompany.ContactName,
		DunsNumber:  dtoCompany.DunsNumber,
		Email:       dtoCompany.Email,
		PhoneNumber: dtoCompany.PhoneNumber,
		WebsiteURL:  dtoCompany.WebsiteURL,
	}
}

func convertRegistSellerRequestToDomain(req *dtos.RegisterSellerRequest) *user.Seller {
	// Convert address, bank account, company info, and kyc from DTO to domain model
	seller := &user.Seller{
		AccountType:    string(req.Seller.AccountType),
		ShopName:       req.Seller.ShopName,
		About:          req.Seller.About,
		AvatarUrl:      req.Seller.AvatarUrl,
		HeaderImgUrl:   req.Seller.HeaderImgUrl,
		Specialty:      req.Seller.Specialty,
		StockLocation:  req.Seller.StockLocation,
		FavoriteBrands: req.Seller.FavoriteBrands,
	}

	// Convert Kyc
	if req.Kyc != nil && req.Kyc.KycType != nil {
		seller.Kyc = &user.Kyc{
			KycType:   string(*req.Kyc.KycType),
			KycImgUrl: pointer.Safe(req.Kyc.KycImgUrl),
		}
	}

	// Convert Address
	if req.Address != nil {
		seller.Address = convertDTOAddressToDomain(req.Address)
	}

	// Convert BankAccount
	if req.BankAccount != nil {
		seller.BankAccount = convertDTOBankAccountToDomain(req.BankAccount)
	}

	// Convert CompanyInfo
	if req.CompanyInfo != nil {
		seller.CompanyInfo = convertDTOCompanyInfoToDomain(req.CompanyInfo)
	}

	return seller
}

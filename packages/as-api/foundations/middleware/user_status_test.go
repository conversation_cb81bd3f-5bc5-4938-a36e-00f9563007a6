package middleware

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"as-api/as/internal/user"
	usermock "as-api/as/mocks/domains/user"
	loggermock "as-api/as/mocks/logger"
	pkgcontext "as-api/as/pkg/context"
	"as-api/as/pkg/jwt"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestUserStatusMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		claims         *jwt.CustomClaims
		isSuspended    bool
		suspendError   error
		expectedStatus int
		requestMethod  string
		requestPath    string
	}{
		{
			name:           "No claims in context - should pass through",
			claims:         nil,
			expectedStatus: http.StatusOK,
		},
		{
			name: "Admin user - should skip check",
			claims: &jwt.CustomClaims{
				UID:     "admin-123",
				IsAdmin: true,
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Regular user - not suspended",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			isSuspended:    false,
			expectedStatus: http.StatusOK,
		},
		{
			name: "Regular user - suspended",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			isSuspended:    true,
			expectedStatus: http.StatusForbidden, // User suspended should return 403
		},
		{
			name: "User without UID - should skip check",
			claims: &jwt.CustomClaims{
				UID:     "",
				IsAdmin: false,
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "User suspended check error - should return error",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			suspendError:   errors.New("database error"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "Regular user with path - not suspended",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			requestMethod:  "GET",
			requestPath:    "/api/users",
			isSuspended:    false,
			expectedStatus: http.StatusOK,
		},
		{
			name: "Regular user with path - suspended",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			requestMethod:  "POST",
			requestPath:    "/api/orders",
			isSuspended:    true,
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "Invalid claims type - should return error",
			claims:         &jwt.CustomClaims{}, // Will be overridden in test
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name: "User with empty UID but not empty string - should check",
			claims: &jwt.CustomClaims{
				UID:     "   ", // Whitespace only
				IsAdmin: false,
			},
			isSuspended:    false,
			expectedStatus: http.StatusOK,
		},
		{
			name: "User with very long UID - should check",
			claims: &jwt.CustomClaims{
				UID:     "very-long-user-id-that-exceeds-normal-length-limits",
				IsAdmin: false,
			},
			isSuspended:    false,
			expectedStatus: http.StatusOK,
		},
		{
			name: "User with special characters in UID - should check",
			claims: &jwt.CustomClaims{
				UID:     "<EMAIL>",
				IsAdmin: false,
			},
			isSuspended:    false,
			expectedStatus: http.StatusOK,
		},
		{
			name: "Regular user with PUT method - not suspended",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			requestMethod:  "PUT",
			requestPath:    "/api/orders",
			isSuspended:    false,
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockUserDomain := usermock.NewUserDomain(t)
			mockLogger := loggermock.NewLogger(t)

			// Setup user domain mock expectations
			if tt.claims != nil && !tt.claims.IsAdmin && tt.claims.UID != "" {
				if tt.isSuspended {
					mockUserDomain.On("GetUserStatusByUserID", mock.Anything, tt.claims.UID).
						Return(user.AccountStatusSuspended, tt.suspendError).Once()
				} else {
					mockUserDomain.On("GetUserStatusByUserID", mock.Anything, tt.claims.UID).
						Return("", tt.suspendError).Once()
				}
			}

			// Setup logger mock expectations for error cases
			if tt.suspendError != nil {
				if tt.expectedStatus == http.StatusInternalServerError {
					mockLogger.On("Error", mock.Anything, mock.Anything).Return().Once()
				} else {
					mockLogger.On("Info", mock.Anything).Return().Once()
				}
			} else if tt.isSuspended {
				mockLogger.On("Info", mock.Anything).Return().Once()
			} else if tt.name == "Invalid claims type - should return error" {
				mockLogger.On("Info", mock.Anything).Return().Once()
			}

			// Create checker
			checker := NewUserStatusChecker(mockUserDomain, mockLogger)

			// Create test handler
			testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				_, err := w.Write([]byte("success"))
				if err != nil {
					t.Errorf("Failed to write response: %v", err)
				}
			})

			// Create middleware
			middleware := UserStatusMiddleware(checker, mockLogger)

			// Create request
			method := tt.requestMethod
			if method == "" {
				method = "GET"
			}
			path := tt.requestPath
			if path == "" {
				path = "/test"
			}
			req := httptest.NewRequest(method, path, nil)

			// Add claims to context if provided
			if tt.claims != nil {
				if tt.name == "Invalid claims type - should return error" {
					// Set invalid claims type to test error handling
					ctx := context.WithValue(req.Context(), pkgcontext.ClaimsCtx, "invalid-type")
					req = req.WithContext(ctx)
				} else {
					ctx := context.WithValue(req.Context(), pkgcontext.ClaimsCtx, *tt.claims)
					req = req.WithContext(ctx)
				}
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute
			middleware(testHandler).ServeHTTP(rr, req)

			// Assert
			assert.Equal(t, tt.expectedStatus, rr.Code)

			if tt.expectedStatus == http.StatusOK {
				assert.Equal(t, "success", rr.Body.String())
			}

			// Verify all expectations were met
			mockUserDomain.AssertExpectations(t)
			mockLogger.AssertExpectations(t)
		})
	}
}

func TestUserStatusChecker_IsUserSuspended(t *testing.T) {
	tests := []struct {
		name        string
		userID      string
		isSuspended bool
		error       error
	}{
		{
			name:        "User not suspended",
			userID:      "user-123",
			isSuspended: false,
			error:       nil,
		},
		{
			name:        "User suspended",
			userID:      "user-456",
			isSuspended: true,
			error:       nil,
		},
		{
			name:        "Error checking user status",
			userID:      "user-789",
			isSuspended: false,
			error:       errors.New("database error"),
		},
		{
			name:        "Empty user ID",
			userID:      "",
			isSuspended: false,
			error:       nil,
		},
		{
			name:        "Very long user ID",
			userID:      "very-long-user-id-that-exceeds-normal-length-limits-and-tests-edge-cases",
			isSuspended: true,
			error:       nil,
		},
		{
			name:        "User ID with special characters",
			userID:      "<EMAIL>",
			isSuspended: false,
			error:       nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			mockUserDomain := usermock.NewUserDomain(t)
			mockLogger := loggermock.NewLogger(t)

			// Setup expectations
			if tt.isSuspended {
				mockUserDomain.On("GetUserStatusByUserID", mock.Anything, tt.userID).
					Return(user.AccountStatusSuspended, tt.error).Once()
			} else {
				mockUserDomain.On("GetUserStatusByUserID", mock.Anything, tt.userID).
					Return("", tt.error).Once()
			}

			// Create checker
			checker := NewUserStatusChecker(mockUserDomain, mockLogger)

			// Test
			result, err := checker.IsUserSuspended(context.Background(), tt.userID)

			// Assert
			assert.Equal(t, tt.isSuspended, result)
			if tt.error != nil {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.error.Error())
			} else {
				assert.NoError(t, err)
			}

			// Verify expectations
			mockUserDomain.AssertExpectations(t)
		})
	}
}

func TestNewUserStatusChecker(t *testing.T) {
	// Test that NewUserStatusChecker creates a valid checker
	mockUserDomain := usermock.NewUserDomain(t)
	mockLogger := loggermock.NewLogger(t)

	checker := NewUserStatusChecker(mockUserDomain, mockLogger)
	assert.NotNil(t, checker)

	// Test that the checker implements the interface
	var _ UserStatusChecker = checker
}

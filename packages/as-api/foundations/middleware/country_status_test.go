package middleware

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"as-api/as/internal/user"
	cmsmock "as-api/as/mocks/domains/cms"
	usermock "as-api/as/mocks/domains/user"
	loggermock "as-api/as/mocks/logger"
	pkgcontext "as-api/as/pkg/context"
	"as-api/as/pkg/jwt"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestCountryStatusMiddleware(t *testing.T) {
	tests := []struct {
		name                 string
		claims               *jwt.CustomClaims
		userData             *user.User
		userError            error
		isCountryDeactivated bool
		countryError         error
		expectedStatus       int
		requestMethod        string
		requestPath          string
	}{
		{
			name:           "No claims in context - should pass through",
			claims:         nil,
			expectedStatus: http.StatusOK,
		},
		{
			name: "Admin user - should skip check",
			claims: &jwt.CustomClaims{
				UID:     "admin-123",
				IsAdmin: true,
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "User without UID - should skip check",
			claims: &jwt.CustomClaims{
				UID:     "",
				IsAdmin: false,
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Regular user with no country code - should pass through",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userData: &user.User{
				ID:          stringPtr("user-123"),
				CountryCode: nil,
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Regular user with empty country code - should pass through",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userData: &user.User{
				ID:          stringPtr("user-123"),
				CountryCode: stringPtr(""),
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "Regular user with valid country code - country not deactivated",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userData: &user.User{
				ID:          stringPtr("user-123"),
				CountryCode: stringPtr("US"),
			},
			isCountryDeactivated: false,
			expectedStatus:       http.StatusOK,
		},
		{
			name: "Regular user with valid country code - country deactivated",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userData: &user.User{
				ID:          stringPtr("user-123"),
				CountryCode: stringPtr("US"),
			},
			isCountryDeactivated: true,
			expectedStatus:       http.StatusForbidden,
		},
		{
			name: "User not found - should return error",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userError:      errors.New("user not found"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "Country check error - should return error",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userData: &user.User{
				ID:          stringPtr("user-123"),
				CountryCode: stringPtr("US"),
			},
			countryError:   errors.New("country not found"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "Regular user with path - country not deactivated",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userData: &user.User{
				ID:          stringPtr("user-123"),
				CountryCode: stringPtr("JP"),
			},
			requestMethod:        "GET",
			requestPath:          "/api/users",
			isCountryDeactivated: false,
			expectedStatus:       http.StatusOK,
		},
		{
			name: "Regular user with path - country deactivated",
			claims: &jwt.CustomClaims{
				UID:     "user-123",
				IsAdmin: false,
			},
			userData: &user.User{
				ID:          stringPtr("user-123"),
				CountryCode: stringPtr("JP"),
			},
			requestMethod:        "POST",
			requestPath:          "/api/orders",
			isCountryDeactivated: true,
			expectedStatus:       http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			userDomainMock := &usermock.UserDomain{}
			cmsDomainMock := &cmsmock.CmsDomain{}
			loggerMock := &loggermock.Logger{}

			// Create checker
			checker := NewCountryStatusChecker(userDomainMock, cmsDomainMock, loggerMock)

			// Create middleware
			middleware := CountryStatusMiddleware(checker, loggerMock)

			// Create test handler
			handlerCalled := false
			testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				handlerCalled = true
				w.WriteHeader(http.StatusOK)
			})

			// Create request
			method := tt.requestMethod
			if method == "" {
				method = "GET"
			}
			path := tt.requestPath
			if path == "" {
				path = "/test"
			}
			req, err := http.NewRequest(method, "http://example.com"+path, nil)
			assert.NoError(t, err)

			// Set up context with claims if provided
			ctx := req.Context()
			if tt.claims != nil {
				ctx = context.WithValue(ctx, pkgcontext.ClaimsCtx, *tt.claims)
				req = req.WithContext(ctx)
			}

			// Set up mock expectations
			if tt.claims != nil && !tt.claims.IsAdmin && tt.claims.UID != "" {
				if tt.userError != nil {
					userDomainMock.On("ReadOne", mock.Anything, tt.claims.UID).Return(nil, tt.userError)
					// Set up logger mock for error case
					if tt.expectedStatus == http.StatusInternalServerError {
						loggerMock.On("Error", mock.Anything, mock.Anything).Return().Once()
					} else {
						loggerMock.On("Info", mock.Anything).Return()
					}
				} else {
					userDomainMock.On("ReadOne", mock.Anything, tt.claims.UID).Return(tt.userData, nil)

					// Only check country if user has a country code
					if tt.userData != nil && tt.userData.CountryCode != nil && *tt.userData.CountryCode != "" {
						cmsDomainMock.On("IsCountryDeactivated", mock.Anything, *tt.userData.CountryCode).Return(tt.isCountryDeactivated, tt.countryError)

						// Set up logger mock for error cases
						if tt.countryError != nil || tt.isCountryDeactivated {
							if tt.expectedStatus == http.StatusInternalServerError {
								loggerMock.On("Error", mock.Anything, mock.Anything).Return().Once()
							} else {
								loggerMock.On("Info", mock.Anything).Return()
							}
						}
					}
				}
			}

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call middleware
			middleware(testHandler).ServeHTTP(rr, req)

			// Assertions
			assert.Equal(t, tt.expectedStatus, rr.Code)

			// Check if handler was called (should be called for successful cases)
			if tt.expectedStatus == http.StatusOK {
				assert.True(t, handlerCalled, "Handler should have been called for successful case")
			} else {
				assert.False(t, handlerCalled, "Handler should not have been called for error case")
			}

			// Verify all mocks
			userDomainMock.AssertExpectations(t)
			cmsDomainMock.AssertExpectations(t)
		})
	}
}

func TestCountryStatusChecker_IsCountryDeactivated(t *testing.T) {
	tests := []struct {
		name          string
		countryCode   string
		isDeactivated bool
		expectedError error
	}{
		{
			name:          "Country not deactivated",
			countryCode:   "US",
			isDeactivated: false,
		},
		{
			name:          "Country deactivated",
			countryCode:   "JP",
			isDeactivated: true,
		},
		{
			name:          "Country not found",
			countryCode:   "XX",
			expectedError: errors.New("country not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			userDomainMock := &usermock.UserDomain{}
			cmsDomainMock := &cmsmock.CmsDomain{}
			loggerMock := &loggermock.Logger{}

			// Create checker
			checker := NewCountryStatusChecker(userDomainMock, cmsDomainMock, loggerMock)

			// Set up mock expectations
			cmsDomainMock.On("IsCountryDeactivated", mock.Anything, tt.countryCode).Return(tt.isDeactivated, tt.expectedError)

			// Call method
			result, err := checker.IsCountryDeactivated(context.Background(), tt.countryCode)

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.isDeactivated, result)
			}

			// Verify mock
			cmsDomainMock.AssertExpectations(t)
		})
	}
}

func TestCountryStatusChecker_GetUserByID(t *testing.T) {
	tests := []struct {
		name          string
		userID        string
		expectedUser  *user.User
		expectedError error
	}{
		{
			name:   "User found",
			userID: "user-123",
			expectedUser: &user.User{
				ID:          stringPtr("user-123"),
				Email:       "<EMAIL>",
				CountryCode: stringPtr("US"),
			},
		},
		{
			name:          "User not found",
			userID:        "user-456",
			expectedError: errors.New("user not found"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mocks
			userDomainMock := &usermock.UserDomain{}
			cmsDomainMock := &cmsmock.CmsDomain{}
			loggerMock := &loggermock.Logger{}

			// Create checker
			checker := NewCountryStatusChecker(userDomainMock, cmsDomainMock, loggerMock)

			// Set up mock expectations
			userDomainMock.On("ReadOne", mock.Anything, tt.userID).Return(tt.expectedUser, tt.expectedError)

			// Call method
			result, err := checker.GetUserByID(context.Background(), tt.userID)

			// Assertions
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedUser, result)
			}

			// Verify mock
			userDomainMock.AssertExpectations(t)
		})
	}
}

func TestNewCountryStatusChecker(t *testing.T) {
	// Create mocks
	userDomainMock := &usermock.UserDomain{}
	cmsDomainMock := &cmsmock.CmsDomain{}
	loggerMock := &loggermock.Logger{}

	// Create checker
	checker := NewCountryStatusChecker(userDomainMock, cmsDomainMock, loggerMock)

	// Assertions
	assert.NotNil(t, checker)

	// Verify it implements the interface
	var _ CountryStatusChecker = checker
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}

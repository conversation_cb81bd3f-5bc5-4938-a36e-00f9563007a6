package logger

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type MockSlackNotifier struct {
	mock.Mock
}

func (m *MockSlackNotifier) SendErrorNotification(ctx context.Context, message string, fields map[string]interface{}) error {
	args := m.Called(ctx, message, fields)
	return args.Error(0)
}

func TestNewLoggerWithSlack(t *testing.T) {
	mockSlack := &MockSlackNotifier{}

	logger, err := NewLoggerWithNotifier(mockSlack)
	assert.NoError(t, err)
	assert.NotNil(t, logger)
}

func TestLogger_Error_WithSlack(t *testing.T) {
	mockSlack := &MockSlackNotifier{}
	mockSlack.On("SendErrorNotification", mock.Anything, "Test error message", mock.Anything).Return(nil)

	logger, err := NewLoggerWithNotifier(mockSlack)
	assert.NoError(t, err)

	logger.Error("Test error message", FieldMap{"user_id": "123"})

	// Wait a bit for the goroutine to complete
	time.Sleep(100 * time.Millisecond)

	mockSlack.AssertExpectations(t)
}

func TestLogger_Errorf_WithSlack(t *testing.T) {
	mockSlack := &MockSlackNotifier{}
	mockSlack.On("SendErrorNotification", mock.Anything, "Test error message with 123", mock.Anything).Return(nil)

	logger, err := NewLoggerWithNotifier(mockSlack)
	assert.NoError(t, err)

	logger.Errorf("Test error message with %s", "123")

	// Wait a bit for the goroutine to complete
	time.Sleep(100 * time.Millisecond)

	mockSlack.AssertExpectations(t)
}

func TestLogger_Error_WithoutSlack(t *testing.T) {
	logger, err := NewLogger()
	assert.NoError(t, err)

	// Should not panic or error when Slack is nil
	logger.Error("Test error message", FieldMap{"user_id": "123"})
}

func TestLogger_WithContext_PreservesSlack(t *testing.T) {
	mockSlack := &MockSlackNotifier{}
	mockSlack.On("SendErrorNotification", mock.Anything, "Test error message", mock.Anything).Return(nil)

	logger, err := NewLoggerWithNotifier(mockSlack)
	assert.NoError(t, err)

	ctx := context.Background()
	childLogger := logger.WithContext(ctx)

	// Child logger should have the same Slack service
	childLogger.Error("Test error message")

	// Wait a bit for the goroutine to complete
	time.Sleep(100 * time.Millisecond)

	mockSlack.AssertExpectations(t)
}

func TestNewAPILoggerWithSlack(t *testing.T) {
	mockSlack := &MockSlackNotifier{}

	logger := NewAPILoggerWithNotifier(mockSlack)
	assert.NotNil(t, logger)
}

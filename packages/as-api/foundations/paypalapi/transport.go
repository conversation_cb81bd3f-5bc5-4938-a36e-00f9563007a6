package paypalapi

import (
	"fmt"
	"net/http"
	"net/http/httputil"

	"as-api/as/foundations/logger"
)

type Transport struct {
	http.RoundTripper
	logger logger.Logger
}

func (t *Transport) RoundTrip(req *http.Request) (*http.Response, error) {
	defer t.logRequest(req)
	return t.RoundTripper.RoundTrip(req)
}

func (t *Transport) logRequest(req *http.Request) {
	b, err := httputil.DumpRequest(req, true)
	if err != nil {
		t.log(err)
	}
	t.log(string(b))
}

func (t *Transport) log(a interface{}) {
	t.logger.Info(fmt.Sprint(a))
}

func NewTransport(t http.RoundTripper, logger logger.Logger) http.RoundTripper {
	return &Transport{
		RoundTripper: t,
		logger:       logger,
	}
}

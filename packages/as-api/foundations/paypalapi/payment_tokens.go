package paypalapi

import (
	"context"

	paymenttokens "as-api/as/foundations/paypalapi/payment-tokens"

	"github.com/pkg/errors"
)

type SetupTokenRequest = paymenttokens.SetupTokenRequest

type SetupTokenResponse = paymenttokens.SetupTokenResponse

// SetupTokensCreate creates a setup token for a given payment source
func (c *paypalClient) SetupTokensCreate(ctx context.Context, req SetupTokenRequest) (*SetupTokenResponse, error) {
	// Create PayPal-Request-Id
	params := &paymenttokens.SetupTokensCreateParams{
		PayPalRequestId: c.createPalPalRequestID(),
	}

	// Call the generated client with authentication
	resp, err := c.paymenttokens.SetupTokensCreateWithResponse(ctx, params, req)
	if err != nil {
		return nil, errors.Wrap(err, "create setup token request")
	}

	if resp.StatusCode() >= 400 {
		return nil, c.handleErrorResponse(resp.StatusCode(), resp.Body)
	}

	if resp.JSON200 != nil {
		return resp.JSON200, nil
	}

	if resp.JSON201 != nil {
		return resp.JSON201, nil
	}

	return nil, errors.Errorf("unexpected response status: %d", resp.StatusCode())
}

type PaymentTokenRequest = paymenttokens.PaymentTokenRequest

type PaymentTokenResponse = paymenttokens.PaymentTokenResponse

// CreateVaultPaymentToken creates a payment token for a given payment source
func (c *paypalClient) CreateVaultPaymentToken(ctx context.Context, req PaymentTokenRequest) (*PaymentTokenResponse, error) {
	// Create PayPal-Request-Id
	params := &paymenttokens.PaymentTokensCreateParams{
		PayPalRequestId: c.createPalPalRequestID(),
	}

	// Call the generated client with authentication
	resp, err := c.paymenttokens.PaymentTokensCreateWithResponse(ctx, params, req)
	if err != nil {
		return nil, errors.Wrap(err, "create payment token request")
	}

	// Handle response
	if resp.StatusCode() >= 400 {
		return nil, c.handleErrorResponse(resp.StatusCode(), resp.Body)
	}

	// Convert successful response
	if resp.JSON200 != nil {
		return resp.JSON200, nil
	}
	if resp.JSON201 != nil {
		return resp.JSON201, nil
	}

	return nil, errors.Errorf("unexpected response status: %d", resp.StatusCode())
}

func (c *paypalClient) DeleteVaultPaymentToken(ctx context.Context, id string) error {
	resp, err := c.paymenttokens.PaymentTokensDeleteWithResponse(ctx, id)
	if err != nil {
		return errors.Wrap(err, "delete payment token request")
	}

	if resp.StatusCode() >= 400 {
		return c.handleErrorResponse(resp.StatusCode(), resp.Body)
	}

	return nil
}

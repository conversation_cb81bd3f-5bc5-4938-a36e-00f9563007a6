// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"

	"gorm.io/gorm"
)

const TableNameNotification = "notifications"

// Notification mapped from table <notifications>
type Notification struct {
	ID           *string        `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt    *time.Time     `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    *time.Time     `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
	SystemType   string         `gorm:"column:system_type;type:character varying(255);not null" json:"system_type"`
	Type         string         `gorm:"column:type;type:character varying(255);not null" json:"type"`
	Title        JSON           `gorm:"column:title;type:jsonb;not null" json:"title"`
	Message      JSON           `gorm:"column:message;type:jsonb;not null" json:"message"`
	Icon         *string        `gorm:"column:icon;type:character varying(255)" json:"icon"`
	Data         *JSON          `gorm:"column:data;type:jsonb" json:"data"`
	Content      *JSON          `gorm:"column:content;type:jsonb;not null;default:{}" json:"content"`
	CountryCodes *StringArray   `gorm:"column:country_codes;type:text[]" json:"country_codes"`
	Status       *string        `gorm:"column:status;type:text;not null;default:draft" json:"status"`
	PublishedAt  *time.Time     `gorm:"column:published_at;type:date" json:"published_at"`
	GroupID      *string        `gorm:"column:group_id;type:character varying(255)" json:"group_id"`
}

// TableName Notification's table name
func (*Notification) TableName() string {
	return TableNameNotification
}

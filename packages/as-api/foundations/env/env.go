package env

import (
	"log"
)

type SecretData struct {
	ServiceName                                       string `json:"SERVICE_NAME" config:"SERVICE_NAME"`
	SupportLink                                       string `json:"SUPPORT_LINK" config:"SUPPORT_LINK"`
	WebappHost                                        string `json:"WEBAPP_HOST_URL" config:"WEBAPP_HOST_URL"`
	ApplinkHost                                       string `json:"APPLINK_HOST_URL" config:"APPLINK_HOST_URL"`
	JWTSecretKey                                      string `json:"JWT_SECRET_KEY" config:"JWT_SECRET_KEY"`
	RJWTSecretKey                                     string `json:"REFRESH_JWT_SECRET_KEY" config:"REFRESH_JWT_SECRET_KEY"`
	ResetPasswordJWTSecretKey                         string `json:"RESET_PASSWORD_JWT_SECRET_KEY" config:"RESET_PASSWORD_JWT_SECRET_KEY"`
	SMTPHost                                          string `json:"SMTP_HOST_URL" config:"SMTP_HOST_URL"`
	SMTPPort                                          string `json:"SMTP_PORT" config:"SMTP_PORT"`
	SMTPUsername                                      string `json:"SMTP_USERNAME" config:"SMTP_USERNAME"`
	SMTPPassword                                      string `json:"SMTP_PASSWORD" config:"SMTP_PASSWORD"`
	SMTPFrom                                          string `json:"SMTP_FROM" config:"SMTP_FROM"`
	AdminEmail                                        string `json:"ADMIN_EMAIL" config:"ADMIN_EMAIL"`
	S3Bucket                                          string `json:"S3_BUCKET" config:"S3_BUCKET"`
	CRONJOB_MASTER_BRANCHS_PUBLISH_TIME               string `json:"CRONJOB_MASTER_BRANCHS_PUBLISH_TIME" config:"CRONJOB_MASTER_BRANCHS_PUBLISH_TIME"`
	CRONJOB_HOMEFEED_CACHE_TIME                       string `json:"CRONJOB_HOMEFEED_CACHE_TIME" config:"CRONJOB_HOMEFEED_CACHE_TIME"`
	CRONJOB_SELLER_DASHBOARD_CACHE_TIME               string `json:"CRONJOB_SELLER_DASHBOARD_CACHE_TIME" config:"CRONJOB_SELLER_DASHBOARD_CACHE_TIME"`
	CRONJOB_FUTURE_NOTIFICATION_TIME                  string `json:"CRONJOB_FUTURE_NOTIFICATION_TIME" config:"CRONJOB_FUTURE_NOTIFICATION_TIME"`
	CRONJOB_SALES_SUMMARY_DAILY_TIME                  string `json:"CRONJOB_SALES_SUMMARY_DAILY_TIME" config:"CRONJOB_SALES_SUMMARY_DAILY_TIME"`
	CRONJOB_SALES_INSIGHTS_DAILY_TIME                 string `json:"CRONJOB_SALES_INSIGHTS_DAILY_TIME" config:"CRONJOB_SALES_INSIGHTS_DAILY_TIME"`
	CRONJOB_EMAIL_NOTIFICATION_TIME                   string `json:"CRONJOB_EMAIL_NOTIFICATION_TIME" config:"CRONJOB_EMAIL_NOTIFICATION_TIME"`
	CRONJOB_APPLE_SUBSCRIPTION_AUDIT_TIME             string `json:"CRONJOB_APPLE_SUBSCRIPTION_AUDIT_TIME" config:"CRONJOB_APPLE_SUBSCRIPTION_AUDIT_TIME"`
	CRONJOB_GOOGLE_SUBSCRIPTION_AUDIT_TIME            string `json:"CRONJOB_GOOGLE_SUBSCRIPTION_AUDIT_TIME" config:"CRONJOB_GOOGLE_SUBSCRIPTION_AUDIT_TIME"`
	CRONJOB_SUBSCRIPTION_EXPIRY_FINALIZER_TIME        string `json:"CRONJOB_SUBSCRIPTION_EXPIRY_FINALIZER_TIME" config:"CRONJOB_SUBSCRIPTION_EXPIRY_FINALIZER_TIME"`
	CRONJOB_PAYMENT_TIMEOUT_TIME                      string `json:"CRONJOB_PAYMENT_TIMEOUT_TIME" config:"CRONJOB_PAYMENT_TIMEOUT_TIME"`
	PackageName                                       string `json:"PACKAGE_NAME" config:"PACKAGE_NAME"`
	GoogleSubscriptionServiceAccount                  string `json:"GOOGLE_SUBSCRIPTION_SERVICE_ACCOUNT" config:"GOOGLE_SUBSCRIPTION_SERVICE_ACCOUNT"`
	AppleSharedSecret                                 string `json:"APPLE_SHARED_SECRET" config:"APPLE_SHARED_SECRET"`
	AppleVerifyURL                                    string `json:"APPLE_VERIFY_URL" config:"APPLE_VERIFY_URL"`
	GMO_MERCHANT                                      string `json:"GMO_MERCHANT" config:"GMO_MERCHANT"`
	GMO_BASE_URL                                      string `json:"GMO_BASE_URL" config:"GMO_BASE_URL"`
	GMO_SHOP_ID                                       string `json:"GMO_SHOP_ID" config:"GMO_SHOP_ID"`
	GMO_SHOP_PASSWORD                                 string `json:"GMO_SHOP_PASSWORD" config:"GMO_SHOP_PASSWORD"`
	CRONJOB_INQUIRY_COUNT_AREA_SERVICE_INQUIRIES_TIME string `json:"CRONJOB_INQUIRY_COUNT_AREA_SERVICE_INQUIRIES_TIME" config:"CRONJOB_INQUIRY_COUNT_AREA_SERVICE_INQUIRIES_TIME"`
	AppleRootCert                                     string `json:"APPLE_ROOT_CERT" config:"APPLE_ROOT_CERT"`
	FirebaseServiceAccount                            string `json:"FIREBASE_SERVICE_ACCOUNT" config:"FIREBASE_SERVICE_ACCOUNT"`
	PayPalClientID                                    string `json:"PAYPAL_CLIENT_ID" config:"PAYPAL_CLIENT_ID"`
	PayPalClientSecret                                string `json:"PAYPAL_CLIENT_SECRET" config:"PAYPAL_CLIENT_SECRET"`
	PayPalBaseURL                                     string `json:"PAYPAL_BASE_URL" config:"PAYPAL_BASE_URL"`
	PayPalWebURL                                      string `json:"PAYPAL_WEB_URL" config:"PAYPAL_WEB_URL"`
	SlackWebhookURL                                   string `json:"SLACK_WEBHOOK_URL" config:"SLACK_WEBHOOK_URL"`
	SlackChannel                                      string `json:"SLACK_CHANNEL" config:"SLACK_CHANNEL"`
}

type RDSData struct {
	PostgresDatabase string `json:"SECRET_POSTGRES_DATABASE" config:"SECRET_POSTGRES_DATABASE"`
	PostgresHostname string `json:"SECRET_POSTGRES_HOSTNAME" config:"SECRET_POSTGRES_HOSTNAME"`
	PostgresPort     string `json:"SECRET_POSTGRES_PORT" config:"SECRET_POSTGRES_PORT"`
	PostgresPassword string `json:"SECRET_POSTGRES_PASSWORD" config:"SECRET_POSTGRES_PASSWORD"`
	PostgresUser     string `json:"SECRET_POSTGRES_USER" config:"SECRET_POSTGRES_USER"`
	PostgresSSLMode  string `json:"SECRET_POSTGRES_SSL_MODE" config:"SECRET_POSTGRES_SSL_MODE"`
}

type MapperData struct {
	SecretData
	RDSData
}

type EnvService interface {
	LoadSecret(interface{})
	LoadRDS(interface{})
}

type ENV interface {
	Load() MapperData
}

type e struct {
	svc  EnvService
	data MapperData
}

func New(svc EnvService) ENV {
	return &e{
		svc: svc,
	}
}

func (e *e) Load() MapperData {
	if e.svc == nil {
		log.Fatal("no env service implement")
	}

	if e.data.SecretData == (SecretData{}) {
		e.svc.LoadSecret(&e.data.SecretData)
	}

	if e.data.RDSData == (RDSData{}) {
		e.svc.LoadRDS(&e.data.RDSData)
	}

	return e.data
}

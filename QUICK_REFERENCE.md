# AS API Quick Reference Guide

## Essential Commands (All in Docker containers!)

### Schema Development
```bash
# 1. Build schema
docker exec -it api.as.dev make build-schema

# 2. Copy docs & generate code
docker exec -it api.as.dev make api-doc ROLE=admin
docker exec -it api.as.dev make dtogen ROLE=admin  
docker exec -it api.as.dev make apigen ROLE=admin tag=system query=1
```

### Database Operations
```bash
# Migration workflow
docker exec -it api.as.dev make migrate
docker exec -it api.as.dev make dbgen
```

### Testing
```bash
docker exec -it api.as.dev make test
docker exec -it api.as.dev make test dir=internal/domain
```

## Feature Implementation Checklist

### 1. OpenAPI Schema (Required First!)
- [ ] Create path files: `packages/as-schema/src/admin/paths/domain/feature/`
- [ ] Create component models: `packages/as-schema/src/admin/components/models/`
- [ ] Create requests/responses: `packages/as-schema/src/admin/components/requests/`
- [ ] Update `index.yml` with references
- [ ] Build schema: `make build-admin`

### 2. Code Generation
- [ ] Copy docs: `make api-doc ROLE=admin`
- [ ] Generate DTOs: `make dtogen ROLE=admin`
- [ ] Generate interfaces: `make apigen ROLE=admin tag=domain query=1`

### 3. Database (if needed)
- [ ] Create DDL: `cmd/migrate/ddl/XXXX_description.sql`
- [ ] Run migration: `make migrate`
- [ ] Generate entities: `make dbgen`

### 4. Implementation
- [ ] Repository interface & implementation
- [ ] Service interface & implementation  
- [ ] API handlers
- [ ] Route registration
- [ ] Dependency injection
- [ ] Tests

## Code Templates

### Repository Interface
```go
type Repository interface {
    GetFeature(ctx context.Context, params GetParams) (*entities.Feature, error)
    UpdateFeature(ctx context.Context, params UpdateParams) error
}
```

### Service Implementation
```go
type serviceImpl struct {
    repo Repository
}

func (s *serviceImpl) GetFeature(ctx context.Context, req *GetRequest) (*Response, error) {
    params := RequestToParams(req)
    entity, err := s.repo.GetFeature(ctx, params)
    if err != nil {
        return nil, err
    }
    return EntityToResponse(entity), nil
}
```

### API Handler
```go
func (h *Handler) GetFeature(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    
    req := &domain.GetRequest{} // populate from request
    
    resp, err := h.service.GetFeature(ctx, req)
    if err != nil {
        apiutil.ErrorResponse(w, err)
        return
    }
    
    dto := &dtos.FeatureResponse{} // map from resp
    apiutil.SuccessResponse(w, dto)
}
```

## OpenAPI Naming Conventions

### Files
- kebab-case: `maintenance-status.yml`
- Paths: `/system/maintenance-status`
- operationId: `get-maintenance-status`

### Schemas  
- PascalCase: `MaintenanceStatus`
- References: `index.yml#/components/schemas/ModelName`

## Common Patterns

### Error Handling
```go
apiutil.ErrorResponse(w, err)
apiutil.BadRequestResponse(w, "message")
apiutil.NotFoundResponse(w, "message")
```

### Testing
```go
func TestService_Method(t *testing.T) {
    tests := []struct {
        name string
        // fields
    }{
        // cases
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // test
        })
    }
}
```

### Dependency Injection
```go
// internal/domain/init.go
func RegisterDependencies(container *dig.Container) error {
    container.Provide(repository.NewRepository)
    container.Provide(NewServiceImpl)
    return nil
}
```

## File Structure Template
```
internal/domain/
├── init.go                 # DI registration
├── service.go             # Service interface
├── service_impl.go        # Service implementation
├── service_test.go        # Service tests
├── mapping.go             # Entity/DTO mapping
├── mapping_test.go        # Mapping tests
└── repository/
    ├── repository.go      # Repository interface
    ├── repository_impl.go # Repository implementation
    └── repository_test.go # Repository tests

api/admin/domain/
├── init.go               # Handler DI registration
├── handler.go            # HTTP handlers
└── handler_test.go       # Handler tests
```

## Critical Reminders

⚠️ **ALWAYS use Docker containers for make commands**
⚠️ **Schema-first approach - create OpenAPI specs before code**
⚠️ **Regenerate DTOs after any schema changes**
⚠️ **Use apiutil for consistent API responses**
⚠️ **Follow Clean Architecture patterns** 